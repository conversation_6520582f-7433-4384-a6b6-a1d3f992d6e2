# Python virtual environments
env/
venv/
.venv/
.env/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.egg-info/

# Development and build artifacts
.git/
.gitignore
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.vscode/
.idea/
.claude/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation and non-essential files
README.md
*.md
docs/
.github/
memory-bank/

# Test and coverage files
.pytest_cache/
.coverage
htmlcov/
.mypy_cache/
.ruff_cache/

# Local development files
.env
uploads/
sessions/
results/
generated_*.csv
docker_test_output_*.csv

# Windows specific
*.lnk

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Log files
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Compiled binaries
*.exe
*.dll
*.so
*.dylib

# Package files
dist/
build/
*.tar.gz
*.zip

# Docker files (don't copy Docker files into Docker)
Dockerfile*
docker-compose*.yml
docker-compose*.yaml