# DataEcho Environment Configuration
# Copy this file to .env and update with your actual values

# LLM Service Configuration
OPENROUTER_API_KEY=your_openrouter_key_here
OPENAI_API_KEY=your_openai_key_here
DEFAULT_MODEL=google/gemini-2.5-flash

# Application Configuration
UPLOAD_DIR=uploads
SESSION_STORAGE_DIR=sessions
MAX_FILE_SIZE=10485760  # 10MB in bytes

# Model Configuration
MODEL_TEMPERATURE=1.0
MODEL_MAX_TOKENS=  # Optional: leave empty for no limit
MODEL_TIMEOUT=     # Optional: leave empty for no timeout
MODEL_MAX_RETRIES=2

# Database Configuration (for future MongoDB integration)
MONGODB_URL=
MONGODB_DB_NAME=dataecho

# WebSocket Configuration
WEBSOCKET_TIMEOUT=300  # 5 minutes

# Docker Hub Configuration (for deployment)
DOCKERHUB_PAT=your_dockerhub_token_here
DOCKER_USERNAME=your_dockerhub_username
DOCKER_TAG=v1

# Development Configuration
DEBUG=true
LOG_LEVEL=INFO

# CORS Configuration (adjust for production)
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]
CORS_ALLOW_CREDENTIALS=true