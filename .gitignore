# Environment variables
.env
!.env.example

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
*.egg-info/
venv/
env/

# IDEs/Editors
.vscode/
.idea/
.claude/

# Windsurf
.windsurfrules

# macOS
.DS_Store

# DataEcho specific
generated_data.csv
generated_*.csv

# Directories for generated content
/uploads/
/results/
/sessions/


# Backend build artifacts
backend/**/__pycache__/
backend/**/.pytest_cache/
backend/**/*.py[cod]
postgres_data/
.venv/

# Coverage reports
htmlcov/
.coverage
coverage.xml

# MyPy cache
.mypy_cache/

# Pytest cache
.pytest_cache/

# Ruff cache
.ruff_cache/

# Build artifacts
dist/
build/
*.egg-info/
