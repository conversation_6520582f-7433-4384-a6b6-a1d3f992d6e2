# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
```bash
# Quick development setup (recommended)
make dev-setup

# Start development server
make run-dev

# Using Docker Compose
docker-compose up --build

# Run all quality checks
make ci-check
```

### Dependencies
```bash
# Install with UV (recommended)
uv pip install -e ".[dev]"

# Or with pip
pip install -e ".[dev]"
```

## Architecture Overview

DataEcho is a multi-agent AI system for synthetic data generation built with FastAPI. The architecture follows a sequential 3-agent workflow:

### Core Agent Pipeline
1. **ProfilerAgent** (`src/dataecho/agents/profiler_agent.py`) - Analyzes CSV structure, data patterns, and characteristics
2. **DependencyAgent** (`src/dataecho/agents/dependency_agent.py`) - Identifies relationships and constraints between fields  
3. **GeneratorAgent** (`src/dataecho/agents/generator_agent.py`) - Creates synthetic data maintaining patterns and dependencies

### Key Components

**WorkflowOrchestrator** (`src/dataecho/orchestrator/workflow_orchestrator.py`)
- Coordinates the sequential execution of all three agents
- Manages session state and progress updates via WebSocket
- Handles error recovery and workflow completion
- Each step updates session progress: profiling (10-30%), dependency analysis (40-60%), generation (70-95%)

**BaseAgent** (`src/dataecho/agents/base_agent.py`)  
- Abstract base class for all agents with common functionality
- Manages agent state, error handling, and LLM service integration
- All agents extend this class and implement `process()` method

**Storage Architecture**
- File-based storage in `uploads/`, `sessions/`, `results/` directories
- Sessions stored as JSON files with UUIDs
- Agent results saved as separate JSON files per session
- Final CSV output generated from generator JSON results

**API Structure** (`backend/api_service/`)
- Modular router-based architecture with separate routers for sessions, generation, WebSockets, and health
- RESTful endpoints for session management, file upload, workflow execution
- WebSocket endpoint (`/ws/{session_id}`) for real-time progress updates
- Background task processing for long-running generation workflows

### Multi-Service Architecture
- **api-service**: FastAPI HTTP + WebSocket layer (port 8000)
- **agent-service**: Heavy-data generation worker (planned for distributed processing)
- **postgres**: Database for future persistence (currently file-based)

### Environment Configuration
Required environment variables in `.env`:
- `OPENROUTER_API_KEY` or `OPENAI_API_KEY`: LLM service authentication
- `DEFAULT_MODEL`: AI model to use (configured in `src/dataecho/config.py`)

### Session Workflow
1. Create session via `POST /api/v1/sessions`
2. Upload CSV via `POST /api/v1/sessions/{id}/upload-csv` 
3. Start generation via `POST /api/v1/sessions/{id}/generate`
4. Monitor progress via WebSocket `/ws/{id}`
5. Download results via `GET /api/v1/sessions/{id}/download`

### Data Flow
```
CSV Upload → ProfilerAgent → DependencyAgent → GeneratorAgent → Final CSV
            ↓              ↓                ↓                ↓
      Profile JSON   Dependency JSON   Generator JSON   Final Output
```

### Important Implementation Notes
- All agents run sequentially (not parallel) in current MVP1
- LangGraph integration planned for MVP2 to enable parallel processing  
- File-based storage will be replaced with MongoDB in future versions
- Each agent saves intermediate results as JSON for debugging and pipeline recovery
- WebSocket connection manager handles real-time progress updates across all workflow steps