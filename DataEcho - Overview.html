<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DataEcho - Technical Architecture & Developer Onboarding</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .mockup-tab { display: none; }
        .mockup-tab.active { display: block; }
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            line-height: 1.4;
        }
        .agent-flow {
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            border-radius: 0.5rem;
            padding: 0.5rem;
            color: white;
            text-align: center;
            margin: 0.25rem;
            min-width: 120px;
        }
        .flow-arrow {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 1.5rem;
        }
        .mvp-progress {
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
        }
        .mvp-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #34d399);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">DE</span>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">DataEcho</h1>
                        <p class="text-sm text-gray-600">AI-Powered Synthetic Data Generation Platform</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Publicis Sapient</div>
                    <div class="text-xs text-gray-400">Internal Product Development</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex space-x-8">
                <button onclick="showTab('overview')" class="tab-button py-4 px-1 border-b-2 border-blue-500 text-blue-600 font-medium">
                    <i class="fas fa-home mr-2"></i>Overview
                </button>
                <button onclick="showTab('architecture')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-sitemap mr-2"></i>Architecture
                </button>
                <button onclick="showTab('agents')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-cogs mr-2"></i>Agent Workflows
                </button>
                <button onclick="showTab('implementation')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-code mr-2"></i>Implementation
                </button>
                <button onclick="showTab('competitive')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-chart-bar mr-2"></i>Market Analysis
                </button>
                <button onclick="showTab('getting-started')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-rocket mr-2"></i>Getting Started
                </button>
                <button onclick="showTab('ui-mockup')" class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                    <i class="fas fa-desktop mr-2"></i>UI Mockup
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Overview Tab -->
        <div id="overview" class="tab-content active">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Project Overview</h2>
                
                <!-- Current Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-yellow-50 p-6 rounded-lg">
                        <div class="text-yellow-600 text-sm font-medium">Current Status</div>
                        <div class="text-2xl font-bold text-yellow-900">MVP1 Functional</div>
                        <div class="text-sm text-yellow-700 mt-1">Basic 3-agent pipeline working</div>
                    </div>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <div class="text-blue-600 text-sm font-medium">Market Opportunity</div>
                        <div class="text-2xl font-bold text-blue-900">$8.87B</div>
                        <div class="text-sm text-blue-700 mt-1">Projected market by 2034</div>
                    </div>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <div class="text-purple-600 text-sm font-medium">Current Architecture</div>
                        <div class="text-2xl font-bold text-purple-900">Sequential</div>
                        <div class="text-sm text-purple-700 mt-1">Simple workflow orchestration</div>
                    </div>
                </div>

                <!-- What is DataEcho -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">What is DataEcho?</h3>
                    <p class="text-gray-700 mb-4">
                        DataEcho is Publicis Sapient's ambitious entry into the synthetic data generation market, designed as a <strong>multi-agent AI system</strong> 
                        that can understand data requirements through natural language, analyze existing patterns, and generate realistic synthetic data 
                        while maintaining privacy compliance and statistical fidelity.
                    </p>
                    <p class="text-gray-700 mb-4">
                        Unlike traditional synthetic data tools that rely on single-model approaches, DataEcho implements a <strong>parallel agent architecture</strong> 
                        where specialized AI agents work simultaneously on different aspects of data generation - from pattern analysis to constraint validation.
                    </p>
                </div>

                <!-- Key Innovation -->
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Core Innovation: Agent-Based Architecture</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Traditional Approach:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Single model processes entire dataset</li>
                                <li>• Sequential processing bottlenecks</li>
                                <li>• Limited domain awareness</li>
                                <li>• Manual configuration required</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">DataEcho's Multi-Agent System:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Specialized agents for specific tasks</li>
                                <li>• Parallel processing for efficiency</li>
                                <li>• Real-time domain enrichment</li>
                                <li>• Natural language interface</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- MVP Roadmap -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">Development Roadmap</h3>
                    
                    <!-- MVP1 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">MVP1 - Basic Implementation</h4>
                            <span class="text-sm font-medium text-green-600">✓ FUNCTIONAL</span>
                        </div>
                        <div class="mvp-progress mb-2">
                            <div class="mvp-fill" style="width: 75%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">Simple 3-agent pipeline, CSV processing, basic REST API</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ FastAPI Backend</div>
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ 3 Core Agents</div>
                            <div class="bg-green-100 text-green-800 px-2 py-1 rounded">✓ CSV Processing</div>
                            <div class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">⚠ Basic Validation</div>
                        </div>

                    </div>

                    <!-- MVP2 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">MVP2 - Enhanced Pipeline</h4>
                            <span class="text-sm font-medium text-orange-600">📋 PLANNED</span>
                        </div>
                        <div class="mvp-progress mb-2">
                            <div class="mvp-fill" style="width: 0%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">LangGraph integration, improved agent coordination, UI foundation</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">LangGraph Setup</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Agent Coordination</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Basic UI</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Workflow Engine</div>
                        </div>
                    </div>

                    <!-- MVP3 -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="font-medium text-gray-900">MVP3 - Domain Intelligence</h4>
                            <span class="text-sm font-medium text-blue-600">🔮 FUTURE</span>
                        </div>
                        <div class="mvp-progress mb-2">
                            <div class="mvp-fill" style="width: 0%"></div>
                        </div>
                        <div class="text-sm text-gray-600 mb-3">RAG integration, domain enrichment, advanced validation</div>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">RAG System</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Domain Enrichment</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Advanced UI</div>
                            <div class="bg-gray-100 text-gray-600 px-2 py-1 rounded">Trust Scoring</div>
                        </div>
                    </div>
                </div>

                <!-- Business Context -->
                <div class="bg-yellow-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Business Context & Market Need</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">Market Pain Points:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• $332M annually spent on test data management</li>
                                <li>• 71% of testing delays due to data availability</li>
                                <li>• 92% struggle with privacy compliance</li>
                                <li>• 78% use production data with minimal masking</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-2">DataEcho's Solution:</h4>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• Natural language data requirements</li>
                                <li>• Privacy-by-design architecture</li>
                                <li>• Real-time domain enrichment</li>
                                <li>• Automated quality validation</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Future Roadmap -->
                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Future Development Roadmap</h2>

                    <div class="mb-8">
                        <p class="text-gray-700 mb-6">
                            DataEcho's vision extends far beyond the current implementation. Our roadmap outlines the evolution
                            from a basic 3-agent system to a sophisticated multi-agent platform capable of handling complex
                            enterprise data generation requirements with domain intelligence and advanced AI capabilities.
                        </p>

                        <!-- MVP2 Enhanced Pipeline -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-semibold text-gray-900">MVP2 - Enhanced Multi-Agent Pipeline</h3>
                                <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">Q2 2025</span>
                            </div>
                            <div class="bg-orange-50 p-6 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Advanced Agent Orchestration</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>True LangGraph Integration:</strong> Replace manual orchestration with sophisticated state graphs</li>
                                            <li>• <strong>Parallel Processing:</strong> Enable concurrent agent execution for improved performance</li>
                                            <li>• <strong>Dynamic Agent Selection:</strong> Intelligent routing based on data characteristics</li>
                                            <li>• <strong>Enhanced State Management:</strong> Complex workflow state tracking and recovery</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Expanded Agent Ecosystem</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>StatisticalProfilerAgent:</strong> Advanced statistical analysis and pattern detection</li>
                                            <li>• <strong>SchemaInferenceAgent:</strong> Intelligent business rule and constraint discovery</li>
                                            <li>• <strong>ValidationAgent:</strong> Comprehensive data quality and integrity validation</li>
                                            <li>• <strong>ExplainabilityAgent:</strong> Transparent generation decision explanations</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- MVP3 Domain Intelligence -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-semibold text-gray-900">MVP3 - Domain Intelligence & RAG Integration</h3>
                                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">Q4 2025</span>
                            </div>
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div>
                                        <h4 class="font-medium text-blue-600 mb-3">Knowledge Enhancement</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>WebSearchAgent:</strong> Real-time market data integration via Tavily/Serper APIs</li>
                                            <li>• <strong>DomainStandardsFetcher:</strong> Industry compliance and regulatory standards</li>
                                            <li>• <strong>TemporalContextAgent:</strong> Time-aware patterns and seasonality analysis</li>
                                            <li>• <strong>LLMSummarizerAgent:</strong> Knowledge synthesis and context integration</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-green-600 mb-3">Advanced Generation</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>SyntheticGeneratorHybrid:</strong> Multi-approach synthesis engine</li>
                                            <li>• <strong>PatternGeneratorAgent:</strong> Complex pattern and structure generation</li>
                                            <li>• <strong>AnomalyInjector:</strong> Intelligent edge case and anomaly injection</li>
                                            <li>• <strong>TestCaseGeneratorAgent:</strong> Automated test scenario creation</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-purple-600 mb-3">Domain Specialization</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>Financial Services:</strong> Regulatory compliance, risk patterns</li>
                                            <li>• <strong>Healthcare:</strong> HIPAA compliance, medical terminology</li>
                                            <li>• <strong>Retail:</strong> Consumer behavior, seasonal trends</li>
                                            <li>• <strong>Telecommunications:</strong> Usage patterns, geographic data</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- MVP4 Enterprise Platform -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-semibold text-gray-900">MVP4 - Enterprise Platform & Advanced AI</h3>
                                <span class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">2026</span>
                            </div>
                            <div class="bg-purple-50 p-6 rounded-lg">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Privacy & Security</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>PrivacyGuardAgent:</strong> GDPR/CCPA compliance validation</li>
                                            <li>• <strong>DriftDetector:</strong> Data drift and bias monitoring</li>
                                            <li>• <strong>GoldenDataTracerAgent:</strong> Complete data lineage tracking</li>
                                            <li>• <strong>TrustScoreAndTraceAgent:</strong> Comprehensive audit trails</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-900 mb-3">Quality Assurance</h4>
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li>• <strong>SemanticConstraintValidator:</strong> Business rule compliance</li>
                                            <li>• <strong>ConfidenceScorer:</strong> Generation reliability metrics</li>
                                            <li>• <strong>DataQualityAssessor:</strong> Comprehensive quality analysis</li>
                                            <li>• <strong>AgentPeerReviewer:</strong> Multi-agent quality coordination</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="mt-6">
                                    <h4 class="font-medium text-gray-900 mb-3">Advanced Capabilities</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div class="bg-white p-4 rounded border">
                                            <h5 class="font-medium text-purple-600 mb-2">Multi-Modal Input</h5>
                                            <p class="text-sm text-gray-700">Support for CSV, JSON, images, natural language, and API integrations</p>
                                        </div>
                                        <div class="bg-white p-4 rounded border">
                                            <h5 class="font-medium text-purple-600 mb-2">Conversational Interface</h5>
                                            <p class="text-sm text-gray-700">Natural language data requirements and iterative refinement</p>
                                        </div>
                                        <div class="bg-white p-4 rounded border">
                                            <h5 class="font-medium text-purple-600 mb-2">Auto-Learning</h5>
                                            <p class="text-sm text-gray-700">Self-improving generation quality through feedback loops</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Technology Evolution -->
                        <div class="mb-8">
                            <h3 class="text-xl font-semibold text-gray-900 mb-4">Technology Evolution Timeline</h3>
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">Current (MVP1)</div>
                                            <div class="text-sm text-gray-600">Basic 3-agent pipeline, sequential processing, file-based storage</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div class="w-4 h-4 bg-orange-500 rounded-full"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">MVP2 (Q2 2025)</div>
                                            <div class="text-sm text-gray-600">LangGraph orchestration, parallel processing, expanded agent ecosystem</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">MVP3 (Q4 2025)</div>
                                            <div class="text-sm text-gray-600">RAG integration, domain intelligence, real-time knowledge enhancement</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-4">
                                        <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">MVP4 (2026)</div>
                                            <div class="text-sm text-gray-600">Enterprise platform, advanced AI, comprehensive privacy and quality assurance</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Architecture Tab -->
        <div id="architecture" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">System Architecture</h2>

                <!-- High-Level Architecture -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">High-Level System Design</h3>
                    <div class="bg-gray-50 p-6 rounded-lg mb-6">
                        <div class="text-center mb-4">
                            <div class="inline-block bg-blue-100 text-blue-800 px-4 py-2 rounded-lg font-medium">Input Sources</div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-file-csv text-green-500 text-2xl mb-2"></i>
                                <div class="font-medium">CSV Files</div>
                                <div class="text-xs text-gray-600">Structured data upload</div>
                            </div>
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-comments text-blue-500 text-2xl mb-2"></i>
                                <div class="font-medium">Natural Language</div>
                                <div class="text-xs text-gray-600">Freeform requirements</div>
                            </div>
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-camera text-purple-500 text-2xl mb-2"></i>
                                <div class="font-medium">Screenshots</div>
                                <div class="text-xs text-gray-600">OCR data extraction</div>
                            </div>
                            <div class="bg-white p-4 rounded border text-center">
                                <i class="fas fa-database text-orange-500 text-2xl mb-2"></i>
                                <div class="font-medium">Domain Selection</div>
                                <div class="text-xs text-gray-600">Industry templates</div>
                            </div>
                        </div>
                        
                        <div class="text-center mb-4">
                            <i class="fas fa-arrow-down text-gray-400 text-2xl"></i>
                        </div>
                        
                        <div class="text-center mb-4">
                            <div class="inline-block bg-green-100 text-green-800 px-4 py-2 rounded-lg font-medium">FastAPI Orchestration Layer</div>
                        </div>
                        
                        <div class="text-center mb-6">
                            <i class="fas fa-arrow-down text-gray-400 text-2xl"></i>
                        </div>
                        
                        <div class="text-center mb-4">
                            <div class="inline-block bg-purple-100 text-purple-800 px-4 py-2 rounded-lg font-medium">LangGraph Agent Framework</div>
                        </div>
                    </div>
                </div>

                <!-- FastAPI Layer -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">FastAPI Orchestration Layer</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Core Components:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Upload + Trigger + Return:</strong> Handles file uploads and initiates processing pipelines</li>
                                    <li><strong>Seed/Config Manager:</strong> Ensures reproducible data generation through seeding</li>
                                    <li><strong>Session Management:</strong> Maintains state across complex generation workflows</li>
                                    <li><strong>API Gateway:</strong> RESTful endpoints for programmatic access</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Key Features:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Multi-format Support:</strong> CSV, JSON, natural language, images</li>
                                    <li><strong>Async Processing:</strong> Non-blocking operations for large datasets</li>
                                    <li><strong>Configuration Persistence:</strong> Save and replay generation parameters</li>
                                    <li><strong>Error Handling:</strong> Robust error management and recovery</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">API Endpoints (Current Implementation):</h4>
                            <div class="code-block"># Session Management
POST /api/v1/sessions                    # Create new session
GET  /api/v1/sessions/{session_id}       # Get session status

# File Upload and Processing
POST /api/v1/sessions/{session_id}/upload-csv  # Upload CSV file
POST /api/v1/sessions/{session_id}/generate    # Start data generation

# Results and Downloads
GET  /api/v1/sessions/{session_id}/results     # Get processing results
GET  /api/v1/sessions/{session_id}/download    # Download generated CSV

# Complete Workflow (All-in-one)
POST /api/v1/run-complete-workflow       # Upload + Process + Download

# WebSocket Support
WS   /ws/{session_id}                    # Real-time progress updates</div>
                        </div>
                    </div>
                </div>

                <!-- Agent Framework Architecture -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Multi-Agent Framework</h3>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The core innovation of DataEcho lies in its multi-agent architecture orchestrated by LangGraph. 
                            Unlike traditional single-model approaches, DataEcho deploys specialized agents that work in parallel 
                            and sequence to handle different aspects of synthetic data generation.
                        </p>
                        
                        <h4 class="font-medium text-gray-900 mb-3">Current Agent Implementation:</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-white p-4 rounded border">
                                <h5 class="font-medium text-blue-600 mb-2">ProfilerAgent</h5>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• Analyzes CSV data structure</li>
                                    <li>• Identifies data types and patterns</li>
                                    <li>• Generates statistical profiles</li>
                                    <li>• Uses pandas for data analysis</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h5 class="font-medium text-green-600 mb-2">DependencyAgent</h5>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• Analyzes data relationships</li>
                                    <li>• Identifies field dependencies</li>
                                    <li>• Maps data constraints</li>
                                    <li>• Uses profiler results as input</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h5 class="font-medium text-purple-600 mb-2">GeneratorAgent</h5>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• Generates synthetic data</li>
                                    <li>• Maintains data relationships</li>
                                    <li>• Supports batch processing</li>
                                    <li>• Outputs CSV format</li>
                                </ul>
                            </div>
                        </div>

                        <h4 class="font-medium text-gray-900 mb-3">Actual Workflow Orchestration:</h4>
                        <div class="bg-white p-4 rounded border">
                            <div class="flex flex-wrap items-center justify-center space-x-2 mb-4">
                                <div class="agent-flow">CSV Upload</div>
                                <div class="flow-arrow">→</div>
                                <div class="agent-flow">ProfilerAgent</div>
                                <div class="flow-arrow">→</div>
                                <div class="agent-flow">DependencyAgent</div>
                                <div class="flow-arrow">→</div>
                                <div class="agent-flow">GeneratorAgent</div>
                            </div>
                            <div class="text-sm text-gray-600 text-center">
                                Simple sequential workflow managed by WorkflowOrchestrator
                            </div>

                        </div>
                    </div>
                </div>

                <!-- RAG and Enrichment System -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">RAG and Domain Enrichment Layer (MVP3)</h3>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The RAG (Retrieval-Augmented Generation) system provides real-time domain knowledge enrichment, 
                            making DataEcho context-aware and industry-intelligent.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Knowledge Sources:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>WebSearchAgent:</strong> Real-time market data and trends (Tavily/Serper integration)</li>
                                    <li><strong>DomainStandardsFetcher:</strong> Industry standards (ISO, PHR, RBI compliance)</li>
                                    <li><strong>TemporalContextAgent:</strong> Time-aware data patterns and seasonality</li>
                                    <li><strong>LLMSummarizerAgent:</strong> Knowledge synthesis and context integration</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Domain Intelligence:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Finance:</strong> Regulatory compliance, market patterns</li>
                                    <li><strong>Healthcare:</strong> HIPAA compliance, medical terminology</li>
                                    <li><strong>Retail:</strong> Consumer behavior, seasonal trends</li>
                                    <li><strong>Telecommunications:</strong> Usage patterns, geographic data</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Enrichment Pipeline Output:</h4>
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-2">
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-file-csv text-green-500 mb-1"></i><br>CSV Output
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-code text-blue-500 mb-1"></i><br>JSON Output
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-chart-bar text-purple-500 mb-1"></i><br>Quality Report
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-book-open text-orange-500 mb-1"></i><br>Explanation Log
                                </div>
                                <div class="bg-white p-3 rounded text-center text-sm">
                                    <i class="fas fa-shield-alt text-red-500 mb-1"></i><br>Trust Score
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Processing Pipeline -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Actual Data Processing Workflow</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The current implementation uses a straightforward sequential workflow managed by the WorkflowOrchestrator class.
                            Each step must complete before the next one begins.
                        </p>

                        <div class="space-y-4">
                            <div class="bg-white p-4 rounded border-l-4 border-blue-500">
                                <h5 class="font-medium text-gray-900 mb-2">1. Session Creation & File Upload</h5>
                                <p class="text-sm text-gray-700">
                                    Creates a session with domain context and user requirements. Uploads and validates CSV file.
                                    Stores session data in JSON files for persistence.
                                </p>
                            </div>

                            <div class="bg-white p-4 rounded border-l-4 border-green-500">
                                <h5 class="font-medium text-gray-900 mb-2">2. Data Profiling (ProfilerAgent)</h5>
                                <p class="text-sm text-gray-700">
                                    Analyzes CSV structure using pandas. Generates statistical profiles and data type analysis.
                                    Uses LLM to create comprehensive data understanding based on domain context.
                                </p>
                            </div>

                            <div class="bg-white p-4 rounded border-l-4 border-purple-500">
                                <h5 class="font-medium text-gray-900 mb-2">3. Dependency Analysis (DependencyAgent)</h5>
                                <p class="text-sm text-gray-700">
                                    Takes profiler results and identifies relationships between data fields.
                                    Maps functional dependencies and constraints using LLM analysis.
                                </p>
                            </div>

                            <div class="bg-white p-4 rounded border-l-4 border-orange-500">
                                <h5 class="font-medium text-gray-900 mb-2">4. Synthetic Data Generation (GeneratorAgent)</h5>
                                <p class="text-sm text-gray-700">
                                    Combines profile and dependency data to generate synthetic records.
                                    Processes in configurable batches with real-time progress updates via WebSocket.
                                </p>
                            </div>

                            <div class="bg-white p-4 rounded border-l-4 border-red-500">
                                <h5 class="font-medium text-gray-900 mb-2">5. Result Storage & Download</h5>
                                <p class="text-sm text-gray-700">
                                    Saves generated data as CSV file. Updates session status to completed.
                                    Provides download endpoint for retrieving the synthetic dataset.
                                </p>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <!-- Agents Tab -->
        <div id="agents" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Agent Workflows & Responsibilities</h2>

                <!-- Core Agent Pipeline -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Actual Agent Pipeline Implementation</h3>
                    <div class="bg-blue-50 p-6 rounded-lg mb-6">
                        <p class="text-gray-700 mb-4">
                            The current implementation uses a simple sequential 3-agent pipeline orchestrated by the WorkflowOrchestrator class.
                            Each agent processes data independently and passes results to the next agent in the chain.
                        </p>

                        <div class="space-y-4">
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-blue-600 mb-2">Step 1: Data Profiling</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">ProfilerAgent Implementation</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Reads CSV using pandas</li>
                                            <li>• Analyzes data structure and types</li>
                                            <li>• Generates statistical summaries</li>
                                            <li>• Uses LLM to create data profiles</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Technical Details</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Uses DeepSeek model via OpenRouter</li>
                                            <li>• Processes up to 100 sample rows</li>
                                            <li>• Outputs JSON profile data</li>
                                            <li>• Includes domain context in prompts</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-green-600 mb-2">Step 2: Dependency Analysis</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">DependencyAgent Implementation</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Takes profiler results as input</li>
                                            <li>• Analyzes field relationships</li>
                                            <li>• Identifies data constraints</li>
                                            <li>• Maps functional dependencies</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Output Format</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• JSON dependency mappings</li>
                                            <li>• Constraint definitions</li>
                                            <li>• Relationship hierarchies</li>
                                            <li>• Business rule patterns</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-purple-600 mb-2">Step 3: Data Generation</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">GeneratorAgent Implementation</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Uses profile + dependency data</li>
                                            <li>• Generates data in batches</li>
                                            <li>• Maintains relationships</li>
                                            <li>• Supports WebSocket progress updates</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h5 class="font-medium text-gray-900 mb-2">Generation Process</h5>
                                        <ul class="text-sm text-gray-700 space-y-1">
                                            <li>• Configurable batch sizes</li>
                                            <li>• JSON to CSV conversion</li>
                                            <li>• Error handling and retries</li>
                                            <li>• Real-time progress tracking</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Agent Implementation Details -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Agent Implementation Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 p-6 rounded-lg">
                            <h4 class="font-medium text-blue-600 mb-3">ProfilerAgent Technical Details</h4>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">Data Analysis</h5>
                                    <p class="text-sm text-gray-700">Uses pandas to read CSV and analyze up to 100 sample rows for statistical profiling.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">LLM Integration</h5>
                                    <p class="text-sm text-gray-700">Sends data sample to DeepSeek model with domain-specific prompts for intelligent analysis.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">Output Format</h5>
                                    <p class="text-sm text-gray-700">Returns JSON profile with data types, patterns, and statistical summaries.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">Memory Management</h5>
                                    <p class="text-sm text-gray-700">Uses LangGraph MemorySaver for conversation context across agent interactions.</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-medium text-green-600 mb-3">DependencyAgent & GeneratorAgent</h4>
                            <div class="space-y-3">
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">DependencyAgent Process</h5>
                                    <p class="text-sm text-gray-700">Takes ProfilerAgent output and analyzes field relationships using LLM reasoning.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">GeneratorAgent Batching</h5>
                                    <p class="text-sm text-gray-700">Generates synthetic data in configurable batches with progress tracking via WebSocket.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">Error Handling</h5>
                                    <p class="text-sm text-gray-700">Implements retry logic and graceful error handling for LLM API failures.</p>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-1">Data Validation</h5>
                                    <p class="text-sm text-gray-700">Validates JSON responses and converts to CSV format for final output.</p>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <!-- Quality Assurance & Validation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Quality Assurance & Validation</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            DataEcho implements a comprehensive approach to data quality, starting with foundational validation
                            in the current implementation and expanding to sophisticated multi-agent quality assurance.
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-green-600 mb-2">✅ Currently Implemented</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• JSON response validation in GeneratorAgent</li>
                                        <li>• Basic error handling and retry logic</li>
                                        <li>• CSV format validation on output</li>
                                        <li>• Progress tracking and status updates</li>
                                        <li>• Session state management</li>
                                    </ul>
                                </div>

                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-blue-600 mb-2">🔧 Basic Validation Process</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Validates LLM JSON responses</li>
                                        <li>• Checks data type consistency</li>
                                        <li>• Ensures batch size compliance</li>
                                        <li>• Handles generation errors gracefully</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-red-600 mb-2">❌ Not Currently Implemented</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• SemanticConstraintValidator</li>
                                        <li>• TrustScoreAndTraceAgent</li>
                                        <li>• ConfidenceScorer</li>
                                        <li>• ExplainabilityAgent</li>
                                        <li>• DataQualityAssessor</li>
                                        <li>• AgentPeerReviewer</li>
                                    </ul>
                                </div>

                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-orange-600 mb-2">📋 Planned for Future MVPs</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Advanced data quality metrics</li>
                                        <li>• Statistical validation against source</li>
                                        <li>• Business rule compliance checking</li>
                                        <li>• Trust scoring and audit trails</li>
                                    </ul>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- Advanced Agent Capabilities -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Advanced Agent Capabilities</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-yellow-50 p-6 rounded-lg">
                            <h4 class="font-medium text-yellow-600 mb-3">
                                <i class="fas fa-shield-alt mr-2"></i>Privacy & Security (Planned)
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>PrivacyGuardAgent:</strong> GDPR/CCPA compliance validation</div>
                                <div><strong>DriftDetector:</strong> Data drift and bias monitoring</div>
                                <div><strong>GoldenDataTracerAgent:</strong> Data lineage tracking</div>
                                <div><strong>AutoFineTuner:</strong> Self-improving generation</div>
                            </div>
                        </div>

                        <div class="bg-indigo-50 p-6 rounded-lg">
                            <h4 class="font-medium text-indigo-600 mb-3">
                                <i class="fas fa-brain mr-2"></i>Intelligence Enhancement (Planned)
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>WebSearchAgent:</strong> Real-time market data via Tavily/Serper</div>
                                <div><strong>DomainStandardsFetcher:</strong> Industry compliance rules</div>
                                <div><strong>TemporalContextAgent:</strong> Time-aware data patterns</div>
                                <div><strong>LLMSummarizerAgent:</strong> Knowledge synthesis</div>
                            </div>
                        </div>

                        <div class="bg-teal-50 p-6 rounded-lg">
                            <h4 class="font-medium text-teal-600 mb-3">
                                <i class="fas fa-cogs mr-2"></i>Process Optimization (Planned)
                            </h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>FeedbackAgent:</strong> Continuous improvement loops</div>
                                <div><strong>TestCaseGeneratorAgent:</strong> Edge case generation</div>
                                <div><strong>AgentPeerReviewer:</strong> Quality coordination</div>
                                <div><strong>ValidationAgent:</strong> Output validation</div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium text-gray-800 mb-2">Current Implementation Status</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <h5 class="font-medium text-green-600 mb-1">✅ Implemented (3 agents)</h5>
                                <ul class="text-gray-700 space-y-1">
                                    <li>• ProfilerAgent - Data analysis</li>
                                    <li>• DependencyAgent - Relationship mapping</li>
                                    <li>• GeneratorAgent - Synthetic data creation</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="font-medium text-orange-600 mb-1">📋 Planned (15+ agents)</h5>
                                <ul class="text-gray-700 space-y-1">
                                    <li>• All privacy, security, and validation agents</li>
                                    <li>• RAG and domain intelligence agents</li>
                                    <li>• Advanced quality assurance agents</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actual Agent Communication & Coordination -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Actual Agent Communication & Coordination</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <p class="text-gray-700 mb-4">
                            The current implementation uses simple sequential processing managed by the WorkflowOrchestrator class.
                            Agents communicate through file-based storage and session management.
                        </p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-green-600 mb-3">✅ Current Implementation:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li>• Sequential agent execution (no parallelism)</li>
                                    <li>• File-based result storage between agents</li>
                                    <li>• Session-based state management</li>
                                    <li>• WebSocket progress updates to clients</li>
                                    <li>• Simple error handling and retry logic</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-orange-600 mb-3">📋 Planned Features:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li>• Parallel agent execution capabilities</li>
                                    <li>• Complex state management with LangGraph</li>
                                    <li>• Multi-file processing coordination</li>
                                    <li>• Advanced dependency resolution</li>
                                    <li>• Real-time agent communication</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Current State Management:</h4>
                            <div class="code-block"># Session-based state (JSON files)
session_data = {
    "session_id": str,
    "domain": str,
    "user_context": str,
    "status": "created|running|completed|failed",
    "current_step": "profiling|dependency_analysis|generating",
    "progress": int,  # 0-100
    "csv_file_path": str,
    "created_at": str,
    "updated_at": str
}

# Agent results stored separately
results/{session_id}_profiler.json
results/{session_id}_dependency.json
results/{session_id}_generator.json
results/{session_id}_final_output.csv</div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <!-- Implementation Tab -->
        <div id="implementation" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Technical Implementation Details</h2>

                <!-- Technology Stack -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Core Technology Stack</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg text-center">
                            <i class="fab fa-python text-blue-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">Python 3.11</h4>
                            <p class="text-sm text-gray-600">Core runtime environment</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg text-center">
                            <i class="fas fa-rocket text-green-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">FastAPI</h4>
                            <p class="text-sm text-gray-600">REST API + WebSocket support</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg text-center">
                            <i class="fas fa-project-diagram text-purple-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">LangGraph</h4>
                            <p class="text-sm text-gray-600">Basic LLM message handling</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg text-center">
                            <i class="fas fa-brain text-orange-500 text-3xl mb-2"></i>
                            <h4 class="font-medium text-gray-900">DeepSeek via OpenRouter</h4>
                            <p class="text-sm text-gray-600">deepseek-prover-v2:free model</p>
                        </div>
                    </div>

                    <!-- Additional Dependencies -->
                    <div class="mt-6">
                        <h4 class="font-medium text-gray-900 mb-3">Key Dependencies:</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                            <div class="bg-gray-100 px-3 py-2 rounded">pandas 2.1.4</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">pydantic</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">uvicorn</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">langchain-openai</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">langchain-core</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">python-multipart</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">aiofiles</div>
                            <div class="bg-gray-100 px-3 py-2 rounded">python-dotenv</div>
                        </div>
                    </div>
                </div>

                <!-- Current MVP1 Implementation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">MVP1 Implementation Status ✅</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Completed Components:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>FastAPI backend with core endpoints</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic agent architecture foundation</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>CSV file upload and processing</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Statistical profiling capabilities</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic relationship detection</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Simple synthetic data generation</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>JSON/CSV output formats</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Actual Project Structure:</h4>
                                <div class="code-block">data-echo/
├── main.py                    # FastAPI application entry point
├── app/
│   ├── agents/               # AI agent implementations
│   │   ├── base_agent.py    # Base agent class
│   │   ├── profiler_agent.py    # Data profiling
│   │   ├── dependency_agent.py  # Dependency analysis
│   │   └── generator_agent.py   # Synthetic data generation
│   ├── orchestrator/        # Workflow orchestration
│   │   └── workflow_orchestrator.py
│   ├── services/            # Business logic services
│   │   ├── session_manager.py
│   │   └── llm_service.py
│   ├── models/              # Pydantic data models
│   │   ├── requests.py
│   │   ├── responses.py
│   │   └── agents.py
│   ├── storage/             # File storage management
│   │   └── storage_manager.py
│   ├── websocket/           # Real-time communication
│   │   └── connection_manager.py
│   ├── utils/               # Utility functions
│   │   ├── logger.py
│   │   └── data_utils.py
│   ├── config.py            # Application configuration
│   └── prompts.py           # LLM prompts
├── uploads/                 # Uploaded CSV files
├── sessions/                # Session data storage
├── results/                 # Generated results
├── requirements.txt         # Python dependencies
├── Dockerfile              # Container configuration
└── docker-compose.yaml     # Docker orchestration</div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Key MVP1 API Usage:</h4>
                            <div class="code-block"># 1. Create session
POST /api/v1/sessions
Body: {
  "domain": "financial service trading",
  "user_context": "Generate trading data for testing"
}

# 2. Upload CSV file
POST /api/v1/sessions/{session_id}/upload-csv
Content-Type: multipart/form-data
Body: { "file": CSV_FILE }

# 3. Start generation
POST /api/v1/sessions/{session_id}/generate
Body: {
  "n_rows": 100,
  "batch_size": 10
}

# 4. Download results
GET /api/v1/sessions/{session_id}/download
Response: Generated CSV file</div>
                        </div>
                    </div>
                </div>

                <!-- MVP2 Planned Implementation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">MVP2 Planned Implementation 📋</h3>
                    <div class="bg-orange-50 p-6 rounded-lg">
                        <p class="text-orange-800 mb-4 font-medium">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            MVP2 development has not yet started. This phase focuses on LangGraph integration and enhanced agent coordination.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Planned Features:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>LangGraph workflow integration</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Multi-agent coordination system</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Enhanced dependency learning</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Parallel processing capabilities</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Basic web UI foundation</li>
                                    <li class="flex items-center"><i class="fas fa-clock text-orange-500 mr-2"></i>Improved validation pipeline</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technical Challenges:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li>• Agent state management complexity</li>
                                    <li>• Parallel processing coordination</li>
                                    <li>• Error handling across agents</li>
                                    <li>• Performance optimization</li>
                                    <li>• Memory management for large datasets</li>
                                </ul>
                            </div>
                        </div>

                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Current vs Planned LangGraph Usage:</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-medium text-green-600 mb-2">✅ Currently Implemented</h5>
                                    <div class="code-block text-xs">from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, MessagesState

# Basic LLM service wrapper
class LLMService:
    def create_graph(self, memory):
        workflow = StateGraph(MessagesState)
        workflow.add_node("model", call_model)
        return workflow.compile(checkpointer=memory)</div>
                                </div>
                                <div>
                                    <h5 class="font-medium text-orange-600 mb-2">📋 Planned for MVP2</h5>
                                    <div class="code-block text-xs">from langgraph import StateGraph
from dataecho.agents import ProfilerAgent, DependencyAgent

# Multi-agent orchestration
workflow = StateGraph(DataEchoState)
workflow.add_node("profiler", ProfilerAgent())
workflow.add_node("dependency", DependencyAgent())
workflow.add_edge("profiler", "dependency")
app = workflow.compile()</div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- MVP3 Future Implementation -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">MVP3 Future Implementation 🔮</h3>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <p class="text-blue-800 mb-4">
                            MVP3 represents the full vision of DataEcho with complete RAG integration, domain intelligence, and advanced UI capabilities.
                        </p>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-blue-600 mb-2">RAG Integration</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• WebSearchAgent (Tavily/Serper)</li>
                                    <li>• Domain standards fetching</li>
                                    <li>• Real-time knowledge updates</li>
                                    <li>• Context-aware generation</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-green-600 mb-2">Advanced Agents</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• PrivacyGuardAgent</li>
                                    <li>• ExplainabilityAgent</li>
                                    <li>• TrustScoreAgent</li>
                                    <li>• AutoFineTuner</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded border">
                                <h4 class="font-medium text-purple-600 mb-2">Professional UI</h4>
                                <ul class="text-sm text-gray-700 space-y-1">
                                    <li>• Natural language interface</li>
                                    <li>• Real-time agent monitoring</li>
                                    <li>• Interactive data visualization</li>
                                    <li>• Advanced configuration</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Development Challenges -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Key Development Challenges</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
                            <h4 class="font-medium text-red-800 mb-2">Agent Coordination Complexity</h4>
                            <p class="text-sm text-red-700">
                                Managing state consistency across multiple agents while enabling parallel processing requires sophisticated orchestration patterns.
                            </p>
                        </div>
                        
                        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                            <h4 class="font-medium text-yellow-800 mb-2">Scalability and Performance</h4>
                            <p class="text-sm text-yellow-700">
                                Large datasets and complex relationships can create performance bottlenecks that need careful optimization.
                            </p>
                        </div>
                        
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <h4 class="font-medium text-blue-800 mb-2">Domain Knowledge Maintenance</h4>
                            <p class="text-sm text-blue-700">
                                Keeping RAG systems updated with current industry standards and regulations requires ongoing investment.
                            </p>
                        </div>
                        
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                            <h4 class="font-medium text-purple-800 mb-2">Quality Assurance</h4>
                            <p class="text-sm text-purple-700">
                                Ensuring generated data maintains statistical fidelity while meeting business constraints across diverse domains.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Architecture Decisions -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Key Architecture Decisions</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technology Choices:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>FastAPI over Flask:</strong> Better async support and automatic OpenAPI documentation</li>
                                    <li><strong>LangGraph over LangChain:</strong> More sophisticated agent orchestration capabilities</li>
                                    <li><strong>Hybrid Generation:</strong> Combines rule-based, statistical, and AI approaches</li>
                                    <li><strong>Multi-modal Input:</strong> Supports diverse input methods for better UX</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Design Principles:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Privacy by Design:</strong> Built-in compliance and data protection</li>
                                    <li><strong>Explainable AI:</strong> Transparent generation process and decisions</li>
                                    <li><strong>Modular Architecture:</strong> Pluggable agents and components</li>
                                    <li><strong>Enterprise Ready:</strong> Scalable, auditable, and integratable</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Competitive Analysis Tab -->
        <div id="competitive" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Market Analysis & Competitive Landscape</h2>

                <!-- Market Overview -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Market Opportunity</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div class="bg-gradient-to-r from-green-400 to-blue-500 text-white p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold mb-2">$310.5M</div>
                            <div class="text-sm opacity-90">Current Market Size (2024)</div>
                        </div>
                        <div class="bg-gradient-to-r from-purple-400 to-pink-500 text-white p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold mb-2">$8.87B</div>
                            <div class="text-sm opacity-90">Projected Market (2034)</div>
                        </div>
                        <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white p-6 rounded-lg text-center">
                            <div class="text-3xl font-bold mb-2">35.2%</div>
                            <div class="text-sm opacity-90">Annual Growth Rate (CAGR)</div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 p-6 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-3">Market Pain Points:</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li>• <strong>$332M</strong> annually spent on test data management (Gartner, 2023)</li>
                                <li>• <strong>71%</strong> of testing delays due to data availability issues</li>
                            </ul>
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li>• <strong>92%</strong> struggle with data privacy compliance</li>
                                <li>• <strong>78%</strong> use production data with minimal masking</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Competitive Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Competitive Landscape</h3>
                    
                    <!-- DataEcho vs Competitors Table -->
                    <div class="overflow-x-auto mb-6">
                        <table class="min-w-full bg-white border border-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Feature</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-green-600 uppercase bg-green-50">DataEcho</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Mostly.ai</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Gretel.ai</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Hazy</th>
                                    <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase">Delphix</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Natural Language Interface</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">Limited</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Multi-Agent Architecture</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Real-time Domain Enrichment</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">Limited</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Multi-modal Input</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV only</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">CSV</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Conversational Data Design</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr class="bg-gray-50">
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Context-Aware Generation</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">Built-in Trust Scoring</td>
                                    <td class="px-4 py-3 text-center text-green-600">✓</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                    <td class="px-4 py-3 text-center text-yellow-500">Experimental</td>
                                    <td class="px-4 py-3 text-center text-red-500">✗</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Detailed Competitor Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Detailed Competitor Analysis</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 p-6 rounded-lg">
                            <h4 class="font-medium text-blue-600 mb-3">Mostly.ai</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> Privacy-first tabular data generation</div>
                                <div><strong>Strengths:</strong> GDPR compliance, statistical fidelity</div>
                                <div><strong>Limitations:</strong> Tabular only, no natural language interface</div>
                                <div><strong>Pricing:</strong> Flexible pay-per-use model</div>
                            </div>
                        </div>
                        
                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-medium text-green-600 mb-3">Gretel.ai</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> API-driven synthetic data platform</div>
                                <div><strong>Strengths:</strong> Multiple data types, robust APIs</div>
                                <div><strong>Limitations:</strong> Traditional ML approach, limited domain awareness</div>
                                <div><strong>Pricing:</strong> Starting at $295/month</div>
                            </div>
                        </div>
                        
                        <div class="bg-purple-50 p-6 rounded-lg">
                            <h4 class="font-medium text-purple-600 mb-3">Hazy</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> Financial services synthetic data</div>
                                <div><strong>Strengths:</strong> Regulatory compliance, industry specialization</div>
                                <div><strong>Limitations:</strong> Narrow industry focus, limited multi-modal</div>
                                <div><strong>Pricing:</strong> Enterprise-focused pricing</div>
                            </div>
                        </div>
                        
                        <div class="bg-orange-50 p-6 rounded-lg">
                            <h4 class="font-medium text-orange-600 mb-3">Delphix</h4>
                            <div class="space-y-2 text-sm">
                                <div><strong>Focus:</strong> Database virtualization and test data management</div>
                                <div><strong>Strengths:</strong> Enterprise infrastructure, data provisioning</div>
                                <div><strong>Limitations:</strong> Virtualization-based, not true synthesis</div>
                                <div><strong>Pricing:</strong> Enterprise licensing model</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- DataEcho's Unique Value Proposition -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">DataEcho's Unique Value Proposition</h3>
                    <div class="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Technical Differentiators:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>First Multi-Agent Architecture:</strong> Parallel specialized intelligence</li>
                                    <li><strong>Natural Language Interface:</strong> Business users can specify requirements</li>
                                    <li><strong>Real-time Domain Enrichment:</strong> Context-aware through RAG systems</li>
                                    <li><strong>Hybrid Generation Approach:</strong> Rules + Statistics + AI</li>
                                    <li><strong>Multi-modal Input Support:</strong> CSV, text, screenshots</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Business Advantages:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li><strong>Faster Time-to-Value:</strong> Conversational requirements gathering</li>
                                    <li><strong>Reduced Technical Expertise:</strong> Natural language interface</li>
                                    <li><strong>Built-in Explainability:</strong> Transparent generation process</li>
                                    <li><strong>Enterprise-Ready:</strong> Publicis Sapient backing and support</li>
                                    <li><strong>Privacy-by-Design:</strong> Compliance built into architecture</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ROI Analysis -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Return on Investment Analysis</h3>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 mb-2">40-70%</div>
                                <div class="text-sm text-gray-700">Reduction in test data creation time</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 mb-2">15%+</div>
                                <div class="text-sm text-gray-700">Minimum savings in developer time</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600 mb-2">85%</div>
                                <div class="text-sm text-gray-700">Reduction in compliance risk</div>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <h4 class="font-medium text-gray-900 mb-3">Competitive ROI Advantages:</h4>
                            <ul class="space-y-2 text-sm text-gray-700">
                                <li>• <strong>Faster requirement gathering</strong> through natural language interface</li>
                                <li>• <strong>Reduced need for data science expertise</strong> due to automated agent intelligence</li>
                                <li>• <strong>Lower integration costs</strong> with enterprise-ready architecture</li>
                                <li>• <strong>Reduced compliance risks</strong> through built-in privacy features</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Market Positioning Strategy -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Strategic Market Positioning</h3>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                            <h4 class="font-medium text-blue-800 mb-2">Phase 1: Publicis Sapient Client Pilots</h4>
                            <p class="text-sm text-blue-700">
                                Leverage existing client relationships to validate product-market fit and gather real-world feedback.
                            </p>
                        </div>
                        
                        <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-400">
                            <h4 class="font-medium text-green-800 mb-2">Phase 2: Industry-Specific Solutions</h4>
                            <p class="text-sm text-green-700">
                                Develop specialized solutions for finance, healthcare, and retail sectors with domain-specific intelligence.
                            </p>
                        </div>
                        
                        <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                            <h4 class="font-medium text-purple-800 mb-2">Phase 3: Horizontal Market Expansion</h4>
                            <p class="text-sm text-purple-700">
                                Expand across industries with generalized synthetic data capabilities and partner ecosystem.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Getting Started Tab -->
        <div id="getting-started" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Getting Started with DataEcho</h2>
                <p class="text-gray-600 mb-8">
                    Welcome to DataEcho! This guide will help you set up the development environment, understand the codebase,
                    and start contributing to this exciting AI-powered synthetic data generation platform.
                </p>

                <!-- Quick Start Options -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🚀 Quick Start Options</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
                            <div class="text-center mb-4">
                                <i class="fab fa-docker text-blue-500 text-3xl"></i>
                            </div>
                            <h4 class="font-medium text-blue-900 mb-2">Docker Compose</h4>
                            <p class="text-sm text-blue-700 mb-3">Recommended for quick setup</p>
                            <div class="text-xs text-blue-600">
                                <div>✓ Isolated environment</div>
                                <div>✓ Automatic builds</div>
                                <div>✓ Volume persistence</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-6 rounded-lg border-2 border-green-200">
                            <div class="text-center mb-4">
                                <i class="fab fa-python text-green-500 text-3xl"></i>
                            </div>
                            <h4 class="font-medium text-green-900 mb-2">Local Python</h4>
                            <p class="text-sm text-green-700 mb-3">Best for active development</p>
                            <div class="text-xs text-green-600">
                                <div>✓ Hot reload</div>
                                <div>✓ Direct debugging</div>
                                <div>✓ IDE integration</div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-6 rounded-lg border-2 border-purple-200">
                            <div class="text-center mb-4">
                                <i class="fas fa-cube text-purple-500 text-3xl"></i>
                            </div>
                            <h4 class="font-medium text-purple-900 mb-2">Manual Docker</h4>
                            <p class="text-sm text-purple-700 mb-3">For custom configurations</p>
                            <div class="text-xs text-purple-600">
                                <div>✓ Full control</div>
                                <div>✓ Custom networking</div>
                                <div>✓ Production-like</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Prerequisites -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">📋 Prerequisites</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Required Software:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-center">
                                        <i class="fab fa-docker text-blue-500 mr-2"></i>
                                        <strong>Docker & Docker Compose</strong> - Container runtime
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fab fa-git-alt text-orange-500 mr-2"></i>
                                        <strong>Git</strong> - Version control
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fab fa-python text-green-500 mr-2"></i>
                                        <strong>Python 3.11+</strong> - For local development (optional)
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">API Keys Required:</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-center">
                                        <i class="fas fa-key text-blue-500 mr-2"></i>
                                        <strong>OpenRouter API Key</strong> - For DeepSeek model (Required)
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-key text-green-500 mr-2"></i>
                                        <strong>OpenAI API Key</strong> - For OpenAI models (Optional)
                                    </li>
                                </ul>
                                <div class="mt-3 p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                                    <p class="text-xs text-yellow-700">
                                        <strong>Note:</strong> OpenRouter provides access to DeepSeek's free tier,
                                        making it cost-effective for development and testing.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Setup Instructions -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">⚙️ Setup Instructions</h3>

                    <!-- Step 1: Clone and Environment -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Step 1: Clone Repository & Setup Environment</h4>
                        <div class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
                            <div class="code-block"># Clone the repository
git clone https://github.com/your-org/data-echo.git
cd data-echo

# Create environment file
touch .env

# Add your API keys to .env file
echo "OPENROUTER_API_KEY=your_openrouter_api_key_here" >> .env
echo "OPENAI_API_KEY=your_openai_api_key_here" >> .env</div>
                        </div>
                    </div>

                    <!-- Step 2: Choose Your Setup -->
                    <div class="mb-6">
                        <h4 class="font-medium text-gray-900 mb-3">Step 2: Choose Your Development Setup</h4>

                        <!-- Docker Compose Tab -->
                        <div class="border rounded-lg">
                            <div class="border-b">
                                <nav class="flex space-x-8 px-6">
                                    <button onclick="showSetupTab('docker-compose')" class="setup-tab-button py-3 px-1 border-b-2 border-blue-500 text-blue-600 font-medium">
                                        Docker Compose (Recommended)
                                    </button>
                                    <button onclick="showSetupTab('local-python')" class="setup-tab-button py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                                        Local Python Development
                                    </button>
                                    <button onclick="showSetupTab('manual-docker')" class="setup-tab-button py-3 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                                        Manual Docker
                                    </button>
                                </nav>
                            </div>

                            <!-- Docker Compose Content -->
                            <div id="docker-compose" class="setup-tab-content p-6">
                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-900 mb-2">Build and Run with Docker Compose:</h5>
                                    <div class="bg-gray-900 text-gray-100 p-4 rounded-lg">
                                        <div class="code-block"># Build and start services
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down</div>
                                    </div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h6 class="font-medium text-green-800 mb-2">✅ What this does:</h6>
                                    <ul class="text-sm text-green-700 space-y-1">
                                        <li>• Builds the Docker image from source (no pre-built images)</li>
                                        <li>• Creates persistent volumes for uploads, sessions, and results</li>
                                        <li>• Automatically loads environment variables from .env file</li>
                                        <li>• Exposes the application on http://localhost:8000</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Local Python Content -->
                            <div id="local-python" class="setup-tab-content p-6" style="display: none;">
                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-900 mb-2">Local Python Development Setup:</h5>
                                    <div class="bg-gray-900 text-gray-100 p-4 rounded-lg">
                                        <div class="code-block"># Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create required directories
mkdir -p uploads sessions results

# Run with hot reload (development)
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

# Or run production mode
python main.py</div>
                                    </div>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h6 class="font-medium text-blue-800 mb-2">🔧 Best for:</h6>
                                    <ul class="text-sm text-blue-700 space-y-1">
                                        <li>• Active development with immediate code changes</li>
                                        <li>• Debugging with IDE integration</li>
                                        <li>• Testing individual components</li>
                                        <li>• Contributing new features</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Manual Docker Content -->
                            <div id="manual-docker" class="setup-tab-content p-6" style="display: none;">
                                <div class="mb-4">
                                    <h5 class="font-medium text-gray-900 mb-2">Manual Docker Build and Run:</h5>
                                    <div class="bg-gray-900 text-gray-100 p-4 rounded-lg">
                                        <div class="code-block"># Build the image
docker build -t dataecho-local .

# Run the container
docker run -d \
  --name dataecho_local \
  --env-file .env \
  -p 8000:8000 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/sessions:/app/sessions \
  -v $(pwd)/results:/app/results \
  dataecho-local

# View logs
docker logs -f dataecho_local

# Stop and remove
docker stop dataecho_local && docker rm dataecho_local</div>
                                    </div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <h6 class="font-medium text-purple-800 mb-2">⚙️ Use cases:</h6>
                                    <ul class="text-sm text-purple-700 space-y-1">
                                        <li>• Custom Docker configurations</li>
                                        <li>• Integration with existing Docker networks</li>
                                        <li>• Production-like testing environments</li>
                                        <li>• CI/CD pipeline integration</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Keys Setup -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🔑 Getting API Keys</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-blue-50 p-6 rounded-lg">
                            <h4 class="font-medium text-blue-600 mb-3">
                                <i class="fas fa-key mr-2"></i>OpenRouter (Required)
                            </h4>
                            <ol class="text-sm text-gray-700 space-y-2">
                                <li>1. Visit <a href="https://openrouter.ai/" class="text-blue-600 hover:underline" target="_blank">https://openrouter.ai/</a></li>
                                <li>2. Sign up for a free account</li>
                                <li>3. Navigate to the "Keys" section</li>
                                <li>4. Create a new API key</li>
                                <li>5. Add to your .env file as <code class="bg-gray-200 px-1 rounded">OPENROUTER_API_KEY</code></li>
                            </ol>
                            <div class="mt-3 p-3 bg-blue-100 rounded">
                                <p class="text-xs text-blue-700">
                                    <strong>💡 Tip:</strong> OpenRouter provides access to DeepSeek's free tier,
                                    which is perfect for development and testing without costs.
                                </p>
                            </div>
                        </div>
                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-medium text-green-600 mb-3">
                                <i class="fas fa-key mr-2"></i>OpenAI (Optional)
                            </h4>
                            <ol class="text-sm text-gray-700 space-y-2">
                                <li>1. Visit <a href="https://platform.openai.com/" class="text-green-600 hover:underline" target="_blank">https://platform.openai.com/</a></li>
                                <li>2. Sign up and add billing information</li>
                                <li>3. Go to API Keys section</li>
                                <li>4. Create a new secret key</li>
                                <li>5. Add to your .env file as <code class="bg-gray-200 px-1 rounded">OPENAI_API_KEY</code></li>
                            </ol>
                            <div class="mt-3 p-3 bg-green-100 rounded">
                                <p class="text-xs text-green-700">
                                    <strong>💰 Note:</strong> OpenAI requires billing setup and charges per token.
                                    Use for production or when you need specific OpenAI models.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Testing Your Setup -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🧪 Testing Your Setup</h3>
                    <div class="space-y-6">
                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">1. Health Check</h4>
                            <div class="bg-gray-900 text-gray-100 p-4 rounded-lg">
                                <div class="code-block"># Test if the API is running
curl http://localhost:8000/

# Expected response:
# {"message":"Multi-Agent Data Generation API is running","status":"healthy"}</div>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">2. Create a Test Session</h4>
                            <div class="bg-gray-900 text-gray-100 p-4 rounded-lg">
                                <div class="code-block"># Create a new session
curl -X POST "http://localhost:8000/api/v1/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "domain": "financial service trading",
    "user_context": "Generate test trading data for development"
  }'

# Expected response:
# {"session_id":"uuid-here","status":"created","message":"Session created successfully"}</div>
                            </div>
                        </div>

                        <div>
                            <h4 class="font-medium text-gray-900 mb-3">3. Explore API Documentation</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-blue-600 mb-2">Interactive Docs</h5>
                                    <p class="text-sm text-gray-700 mb-2">FastAPI's built-in Swagger UI</p>
                                    <a href="http://localhost:8000/docs" class="text-blue-600 hover:underline text-sm" target="_blank">
                                        http://localhost:8000/docs
                                    </a>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h5 class="font-medium text-green-600 mb-2">Alternative Docs</h5>
                                    <p class="text-sm text-gray-700 mb-2">ReDoc documentation interface</p>
                                    <a href="http://localhost:8000/redoc" class="text-green-600 hover:underline text-sm" target="_blank">
                                        http://localhost:8000/redoc
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Development Workflow -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🔄 Development Workflow</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Daily Development:</h4>
                                <ol class="text-sm text-gray-700 space-y-2">
                                    <li>1. <strong>Pull latest changes:</strong> <code class="bg-gray-200 px-1 rounded">git pull origin main</code></li>
                                    <li>2. <strong>Create feature branch:</strong> <code class="bg-gray-200 px-1 rounded">git checkout -b feature/your-feature</code></li>
                                    <li>3. <strong>Start development server:</strong> Use local Python with <code class="bg-gray-200 px-1 rounded">--reload</code></li>
                                    <li>4. <strong>Make changes:</strong> Edit code and see immediate updates</li>
                                    <li>5. <strong>Test changes:</strong> Use API docs or curl commands</li>
                                    <li>6. <strong>Commit and push:</strong> Standard Git workflow</li>
                                </ol>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Useful Commands:</h4>
                                <div class="space-y-3">
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">View logs:</div>
                                        <code class="text-xs bg-gray-200 px-2 py-1 rounded block">docker-compose logs -f</code>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">Rebuild after changes:</div>
                                        <code class="text-xs bg-gray-200 px-2 py-1 rounded block">docker-compose up --build</code>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">Clean restart:</div>
                                        <code class="text-xs bg-gray-200 px-2 py-1 rounded block">docker-compose down && docker-compose up --build</code>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-800">Check running containers:</div>
                                        <code class="text-xs bg-gray-200 px-2 py-1 rounded block">docker ps</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Structure for Developers -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">📁 Understanding the Codebase</h3>
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Key Files & Directories:</h4>
                                <div class="space-y-2 text-sm">
                                    <div><code class="bg-gray-200 px-1 rounded">main.py</code> - FastAPI application entry point</div>
                                    <div><code class="bg-gray-200 px-1 rounded">app/agents/</code> - AI agent implementations</div>
                                    <div><code class="bg-gray-200 px-1 rounded">app/orchestrator/</code> - Workflow coordination</div>
                                    <div><code class="bg-gray-200 px-1 rounded">app/services/</code> - Business logic services</div>
                                    <div><code class="bg-gray-200 px-1 rounded">app/models/</code> - Pydantic data models</div>
                                    <div><code class="bg-gray-200 px-1 rounded">app/config.py</code> - Configuration management</div>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-3">Runtime Directories:</h4>
                                <div class="space-y-2 text-sm">
                                    <div><code class="bg-gray-200 px-1 rounded">uploads/</code> - User-uploaded CSV files</div>
                                    <div><code class="bg-gray-200 px-1 rounded">sessions/</code> - Session state (JSON files)</div>
                                    <div><code class="bg-gray-200 px-1 rounded">results/</code> - Agent outputs and generated data</div>
                                </div>
                                <div class="mt-4 p-3 bg-blue-50 rounded">
                                    <p class="text-xs text-blue-700">
                                        <strong>💡 Tip:</strong> These directories are automatically created and
                                        mounted as Docker volumes for data persistence.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contributing Guidelines -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🤝 Contributing to DataEcho</h3>
                    <div class="space-y-6">
                        <div class="bg-green-50 p-6 rounded-lg">
                            <h4 class="font-medium text-green-600 mb-3">Areas Where We Need Help:</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-2">🤖 Agent Development</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Implement new specialized agents</li>
                                        <li>• Improve existing agent logic</li>
                                        <li>• Add domain-specific knowledge</li>
                                        <li>• Enhance validation capabilities</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-2">🔧 Infrastructure</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• LangGraph integration</li>
                                        <li>• Database integration (MongoDB)</li>
                                        <li>• Performance optimizations</li>
                                        <li>• Testing and CI/CD</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-2">🎨 User Experience</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Web UI development</li>
                                        <li>• API improvements</li>
                                        <li>• Documentation</li>
                                        <li>• User onboarding</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-900 mb-2">🔬 Research & Innovation</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• RAG integration</li>
                                        <li>• Privacy-preserving techniques</li>
                                        <li>• Quality metrics</li>
                                        <li>• Domain expertise</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="bg-blue-50 p-6 rounded-lg">
                            <h4 class="font-medium text-blue-600 mb-3">Getting Started with Contributions:</h4>
                            <ol class="text-sm text-gray-700 space-y-2">
                                <li>1. <strong>Explore the codebase:</strong> Start with the 3 core agents to understand the pattern</li>
                                <li>2. <strong>Run the system:</strong> Upload a CSV and see the complete workflow in action</li>
                                <li>3. <strong>Pick an area:</strong> Choose something that interests you from the roadmap</li>
                                <li>4. <strong>Start small:</strong> Begin with bug fixes or small improvements</li>
                                <li>5. <strong>Ask questions:</strong> Don't hesitate to reach out to the team</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- See DataEcho in Action -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🎯 See DataEcho in Action</h3>
                    <div class="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg border-2 border-blue-200">
                        <p class="text-gray-700 mb-6">
                            The best way to understand DataEcho is to see it generate synthetic data! Follow these steps to witness
                            AI-powered data generation that maintains relationships and domain intelligence.
                        </p>

                        <!-- Sample Data Creation -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Step 1: Create Sample Data</h4>
                            <p class="text-sm text-gray-600 mb-3">Create a file called <code class="bg-gray-200 px-1 rounded">sample_trading_data.csv</code>:</p>
                            <div class="bg-gray-900 text-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
                                <div class="code-block">trader_id,symbol,trade_type,quantity,price,timestamp,profit_loss,risk_level
T001,AAPL,BUY,100,150.25,2024-01-15 09:30:00,2500.50,LOW
T002,GOOGL,SELL,50,2800.75,2024-01-15 10:15:00,-1200.25,MEDIUM
T003,MSFT,BUY,200,380.50,2024-01-15 11:00:00,3200.00,LOW
T001,TSLA,BUY,75,245.80,2024-01-15 14:30:00,-850.75,HIGH
T004,AMZN,SELL,30,3200.25,2024-01-15 15:45:00,1500.80,MEDIUM
T002,AAPL,BUY,150,148.90,2024-01-16 09:45:00,1800.25,LOW
T003,NVDA,BUY,80,520.75,2024-01-16 11:30:00,4200.60,HIGH
T005,META,SELL,60,485.30,2024-01-16 13:15:00,-950.40,MEDIUM
T004,GOOGL,BUY,25,2750.50,2024-01-16 14:00:00,1250.75,LOW
T001,MSFT,SELL,100,385.25,2024-01-16 16:30:00,2100.50,MEDIUM</div>
                            </div>
                        </div>

                        <!-- API Workflow -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Step 2: Complete API Workflow</h4>
                            <div class="space-y-4">
                                <div>
                                    <h5 class="font-medium text-blue-600 mb-2">Create Session:</h5>
                                    <div class="bg-gray-900 text-gray-100 p-3 rounded text-xs overflow-x-auto">
                                        <div class="code-block">curl -X POST "http://localhost:8000/api/v1/sessions" \
  -H "Content-Type: application/json" \
  -d '{
    "domain": "financial service trading",
    "user_context": "Generate realistic trading data for testing algorithms"
  }'</div>
                                    </div>
                                </div>

                                <div>
                                    <h5 class="font-medium text-green-600 mb-2">Upload CSV (replace SESSION_ID):</h5>
                                    <div class="bg-gray-900 text-gray-100 p-3 rounded text-xs overflow-x-auto">
                                        <div class="code-block">curl -X POST "http://localhost:8000/api/v1/sessions/SESSION_ID/upload-csv" \
  -F "file=@sample_trading_data.csv"</div>
                                    </div>
                                </div>

                                <div>
                                    <h5 class="font-medium text-purple-600 mb-2">Generate Data:</h5>
                                    <div class="bg-gray-900 text-gray-100 p-3 rounded text-xs overflow-x-auto">
                                        <div class="code-block">curl -X POST "http://localhost:8000/api/v1/sessions/SESSION_ID/generate" \
  -H "Content-Type: application/json" \
  -d '{"n_rows": 50, "batch_size": 10}'</div>
                                    </div>
                                </div>

                                <div>
                                    <h5 class="font-medium text-orange-600 mb-2">Download Results:</h5>
                                    <div class="bg-gray-900 text-gray-100 p-3 rounded text-xs overflow-x-auto">
                                        <div class="code-block">curl "http://localhost:8000/api/v1/sessions/SESSION_ID/download" \
  -o generated_trading_data.csv</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- One-Command Alternative -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">Step 3: Or Use One-Command Workflow</h4>
                            <div class="bg-indigo-50 p-4 rounded-lg">
                                <p class="text-sm text-indigo-700 mb-3">For quick testing, use the complete workflow endpoint:</p>
                                <div class="bg-gray-900 text-gray-100 p-3 rounded text-xs overflow-x-auto">
                                    <div class="code-block">curl -X POST "http://localhost:8000/api/v1/run-complete-workflow" \
  -F "file=@sample_trading_data.csv" \
  -F "domain=financial service trading" \
  -F "user_context=Generate realistic trading data" \
  -F "n_rows=30" \
  -F "batch_size=10" \
  --output generated_data.csv</div>
                                </div>
                            </div>
                        </div>

                        <!-- What You'll See -->
                        <div class="mb-6">
                            <h4 class="font-medium text-gray-900 mb-3">🎉 What You'll Witness</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-green-600 mb-2">AI Analysis in Action:</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• ProfilerAgent analyzing data patterns</li>
                                        <li>• DependencyAgent mapping relationships</li>
                                        <li>• GeneratorAgent creating synthetic records</li>
                                        <li>• Real-time progress updates</li>
                                    </ul>
                                </div>
                                <div class="bg-white p-4 rounded border">
                                    <h5 class="font-medium text-blue-600 mb-2">Intelligent Results:</h5>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• Same trader IDs (T001, T002, etc.)</li>
                                        <li>• Realistic stock prices and movements</li>
                                        <li>• Logical timestamp sequences</li>
                                        <li>• Preserved data relationships</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Persistence Explanation -->
                        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                            <h5 class="font-medium text-yellow-800 mb-2">💡 Data Persistence Magic</h5>
                            <p class="text-sm text-yellow-700">
                                <strong>No Database Required!</strong> DataEcho uses Docker volumes to persist data across container restarts.
                                Your sessions, uploads, and results are stored in local directories that survive container rebuilds.
                                Check the <code class="bg-yellow-100 px-1 rounded">sessions/</code>, <code class="bg-yellow-100 px-1 rounded">uploads/</code>,
                                and <code class="bg-yellow-100 px-1 rounded">results/</code> folders to see the magic!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">🔧 Troubleshooting</h3>
                    <div class="space-y-4">
                        <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-400">
                            <h5 class="font-medium text-red-800 mb-2">Common Issues:</h5>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <div class="font-medium text-red-700">Port 8000 already in use:</div>
                                    <div class="text-red-600">Solution: <code class="bg-red-100 px-1 rounded">docker-compose down</code> or change port in docker-compose.yaml</div>
                                </div>
                                <div>
                                    <div class="font-medium text-red-700">API key errors:</div>
                                    <div class="text-red-600">Solution: Check .env file exists and contains valid OPENROUTER_API_KEY</div>
                                </div>
                                <div>
                                    <div class="font-medium text-red-700">Permission denied on volumes:</div>
                                    <div class="text-red-600">Solution: Ensure Docker has access to the project directory</div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-400">
                            <h5 class="font-medium text-yellow-800 mb-2">Need Help?</h5>
                            <p class="text-sm text-yellow-700">
                                If you encounter issues not covered here, check the logs with <code class="bg-yellow-100 px-1 rounded">docker-compose logs -f</code>
                                and feel free to reach out to the development team or create an issue in the repository.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- UI Mockup Tab -->
        <div id="ui-mockup" class="tab-content">
            <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">DataEcho UI Mockup</h2>
                <p class="text-gray-600 mb-6">
                    Professional interface mockup showing the envisioned user experience for DataEcho's multi-agent synthetic data generation platform.
                </p>

                <!-- Mockup Navigation -->
                <div class="border-b mb-6">
                    <nav class="flex space-x-8">
                        <button onclick="showMockupTab('dashboard')" class="mockup-tab-button py-2 px-1 border-b-2 border-blue-500 text-blue-600 font-medium">
                            Dashboard
                        </button>
                        <button onclick="showMockupTab('generation')" class="mockup-tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Data Generation
                        </button>
                        <button onclick="showMockupTab('agent-monitor')" class="mockup-tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Agent Monitor
                        </button>
                        <button onclick="showMockupTab('results')" class="mockup-tab-button py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Results
                        </button>
                    </nav>
                </div>

                <!-- Dashboard Mockup -->
                <div id="dashboard" class="mockup-tab active">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <!-- Header -->
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-red-500 rounded-lg flex items-center justify-center">
                                        <span class="text-white font-bold">DE</span>
                                    </div>
                                    <div>
                                        <h1 class="text-xl font-bold text-gray-900">DataEcho</h1>
                                        <p class="text-sm text-gray-600">AI-Powered Synthetic Data Generation</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="text-sm text-gray-500">Welcome, Developer</div>
                                    <div class="w-8 h-8 bg-gray-300 rounded-full"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-blue-600">12</div>
                                <div class="text-sm text-gray-600">Active Projects</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-green-600">2.3M</div>
                                <div class="text-sm text-gray-600">Records Generated</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-purple-600">94%</div>
                                <div class="text-sm text-gray-600">Quality Score</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-orange-600">8</div>
                                <div class="text-sm text-gray-600">Active Agents</div>
                            </div>
                        </div>

                        <!-- Recent Projects -->
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Projects</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <div>
                                            <div class="font-medium">Customer Demographics - Bank ABC</div>
                                            <div class="text-sm text-gray-600">Generated 100K records • 2 hours ago</div>
                                        </div>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">View</button>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div>
                                            <div class="font-medium">Transaction Data - E-commerce</div>
                                            <div class="text-sm text-gray-600">In progress • 15 min remaining</div>
                                        </div>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">Monitor</button>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <div>
                                            <div class="font-medium">Healthcare Patient Records</div>
                                            <div class="text-sm text-gray-600">HIPAA compliant • Yesterday</div>
                                        </div>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">Download</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Generation Mockup -->
                <div id="generation" class="mockup-tab">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Create Synthetic Data</h3>
                            
                            <!-- Input Methods -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="border-2 border-blue-500 bg-blue-50 p-4 rounded-lg cursor-pointer">
                                    <div class="text-center">
                                        <i class="fas fa-comments text-blue-500 text-2xl mb-2"></i>
                                        <h4 class="font-medium text-blue-900">Natural Language</h4>
                                        <p class="text-sm text-blue-700">Describe your data needs</p>
                                    </div>
                                </div>
                                <div class="border-2 border-gray-200 p-4 rounded-lg cursor-pointer hover:border-blue-300">
                                    <div class="text-center">
                                        <i class="fas fa-file-csv text-green-500 text-2xl mb-2"></i>
                                        <h4 class="font-medium text-gray-700">Upload CSV</h4>
                                        <p class="text-sm text-gray-600">Use existing data as template</p>
                                    </div>
                                </div>
                                <div class="border-2 border-gray-200 p-4 rounded-lg cursor-pointer hover:border-blue-300">
                                    <div class="text-center">
                                        <i class="fas fa-camera text-purple-500 text-2xl mb-2"></i>
                                        <h4 class="font-medium text-gray-700">Screenshot</h4>
                                        <p class="text-sm text-gray-600">Extract from images</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Natural Language Input -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Describe your data requirements:</label>
                                <textarea class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                         rows="4" 
                                         placeholder="I need customer data for a retail bank with 50,000 records including demographics, account information, transaction history, and risk profiles. The data should be GDPR compliant and include realistic patterns for fraud detection testing."></textarea>
                            </div>

                            <!-- Configuration Options -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Number of Records</label>
                                    <input type="number" class="w-full p-3 border border-gray-300 rounded-lg" value="50000">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Industry Domain</label>
                                    <select class="w-full p-3 border border-gray-300 rounded-lg">
                                        <option>Financial Services</option>
                                        <option>Healthcare</option>
                                        <option>Retail</option>
                                        <option>Telecommunications</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Compliance</label>
                                    <select class="w-full p-3 border border-gray-300 rounded-lg">
                                        <option>GDPR + CCPA</option>
                                        <option>HIPAA</option>
                                        <option>PCI DSS</option>
                                        <option>Custom</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-4">
                                <button class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium">
                                    <i class="fas fa-magic mr-2"></i>Generate Data
                                </button>
                                <button class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 font-medium">
                                    <i class="fas fa-save mr-2"></i>Save Configuration
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Monitor Mockup -->
                <div id="agent-monitor" class="mockup-tab">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <h3 class="text-lg font-semibold text-gray-900 mb-6">Agent Activity Monitor</h3>
                            
                            <!-- Agent Pipeline Status -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-gray-900">Current Pipeline: Customer Demographics Generation</h4>
                                    <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">In Progress</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                </div>
                                <div class="text-sm text-gray-600 mt-1">65% Complete • Estimated 8 minutes remaining</div>
                            </div>

                            <!-- Active Agents -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                            <div>
                                                <div class="font-medium text-green-900">ProfilerAgent</div>
                                                <div class="text-sm text-green-700">Analyzing data structure</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-green-600">Active</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                                            <div>
                                                <div class="font-medium text-blue-900">DependencyLearnerAgent</div>
                                                <div class="text-sm text-blue-700">Mapping relationships</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-blue-600">Active</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-purple-900">WebSearchAgent</div>
                                                <div class="text-sm text-purple-700">Fetching domain data</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-purple-600">Queued</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-gray-700">SyntheticGeneratorHybrid</div>
                                                <div class="text-sm text-gray-600">Waiting for dependencies</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-500">Waiting</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-gray-700">ValidationAgent</div>
                                                <div class="text-sm text-gray-600">Waiting for generation</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-500">Waiting</div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                            <div>
                                                <div class="font-medium text-gray-700">TrustScoreAgent</div>
                                                <div class="text-sm text-gray-600">Waiting for validation</div>
                                            </div>
                                        </div>
                                        <div class="text-sm text-gray-500">Waiting</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Agent Logs -->
                            <div class="mt-6">
                                <h4 class="font-medium text-gray-900 mb-3">Recent Agent Activity</h4>
                                <div class="bg-gray-100 p-4 rounded-lg font-mono text-sm max-h-32 overflow-y-auto">
                                    <div class="text-green-600">[14:23:15] ProfilerAgent: Started data structure analysis</div>
                                    <div class="text-blue-600">[14:23:18] ProfilerAgent: Detected 15 columns with mixed data types</div>
                                    <div class="text-green-600">[14:23:22] DependencyLearnerAgent: Initiated relationship mapping</div>
                                    <div class="text-blue-600">[14:23:25] DependencyLearnerAgent: Found 8 potential relationships</div>
                                    <div class="text-purple-600">[14:23:28] WebSearchAgent: Queued for banking domain enrichment</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Mockup -->
                <div id="results" class="mockup-tab">
                    <div class="bg-gray-50 p-6 rounded-lg border-2 border-dashed border-gray-300">
                        <div class="bg-white p-6 rounded-lg shadow-sm">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-lg font-semibold text-gray-900">Generation Results</h3>
                                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">✓ Complete</span>
                            </div>

                            <!-- Quality Metrics -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                <div class="bg-green-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-green-600 mb-1">94%</div>
                                    <div class="text-sm text-gray-600">Overall Quality</div>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-blue-600 mb-1">98%</div>
                                    <div class="text-sm text-gray-600">Statistical Fidelity</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-purple-600 mb-1">92%</div>
                                    <div class="text-sm text-gray-600">Privacy Score</div>
                                </div>
                                <div class="bg-orange-50 p-4 rounded-lg text-center">
                                    <div class="text-2xl font-bold text-orange-600 mb-1">96%</div>
                                    <div class="text-sm text-gray-600">Constraint Compliance</div>
                                </div>
                            </div>

                            <!-- Data Preview -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="font-medium text-gray-900">Data Preview (50,000 records generated)</h4>
                                    <div class="space-x-2">
                                        <button class="bg-blue-600 text-white px-4 py-2 rounded text-sm hover:bg-blue-700">
                                            <i class="fas fa-download mr-1"></i>Download CSV
                                        </button>
                                        <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded text-sm hover:bg-gray-50">
                                            <i class="fas fa-code mr-1"></i>Export JSON
                                        </button>
                                    </div>
                                </div>
                                <div class="overflow-x-auto border border-gray-200 rounded-lg">
                                    <table class="min-w-full bg-white">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">customer_id</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">name</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">email</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">age</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">balance</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">risk_score</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            <tr>
                                                <td class="px-4 py-2 text-sm text-gray-900">CUST-001847</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">Sarah Johnson</td>
                                                <td class="px-4 py-2 text-sm text-gray-900"><EMAIL></td>
                                                <td class="px-4 py-2 text-sm text-gray-900">34</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">$12,450.00</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">0.23</td>
                                            </tr>
                                            <tr class="bg-gray-50">
                                                <td class="px-4 py-2 text-sm text-gray-900">CUST-002156</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">Michael Chen</td>
                                                <td class="px-4 py-2 text-sm text-gray-900"><EMAIL></td>
                                                <td class="px-4 py-2 text-sm text-gray-900">42</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">$8,900.50</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">0.45</td>
                                            </tr>
                                            <tr>
                                                <td class="px-4 py-2 text-sm text-gray-900">CUST-003291</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">Emma Rodriguez</td>
                                                <td class="px-4 py-2 text-sm text-gray-900"><EMAIL></td>
                                                <td class="px-4 py-2 text-sm text-gray-900">28</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">$5,675.25</td>
                                                <td class="px-4 py-2 text-sm text-gray-900">0.18</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Generation Report -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-medium text-gray-900 mb-3">Generation Report</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <div class="font-medium text-gray-700 mb-2">Execution Summary:</div>
                                        <ul class="space-y-1 text-gray-600">
                                            <li>• Generated 50,000 records in 12 minutes</li>
                                            <li>• Used 8 specialized agents</li>
                                            <li>• Applied GDPR compliance rules</li>
                                            <li>• Maintained 94% statistical fidelity</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-700 mb-2">Quality Checks:</div>
                                        <ul class="space-y-1 text-gray-600">
                                            <li>• ✓ All constraints satisfied</li>
                                            <li>• ✓ No sensitive data exposure</li>
                                            <li>• ✓ Relationship integrity maintained</li>
                                            <li>• ✓ Domain standards compliance</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    © 2025 Publicis Sapient. DataEcho - Internal Product Development.
                </div>
                <div class="text-sm text-gray-500">
                    For internal use only. Not ready for external demo.
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked tab button
            event.target.classList.remove('border-transparent', 'text-gray-500');
            event.target.classList.add('border-blue-500', 'text-blue-600');
        }

        function showSetupTab(tabName) {
            // Hide all setup tab contents
            const setupTabContents = document.querySelectorAll('.setup-tab-content');
            setupTabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all setup tab buttons
            const setupTabButtons = document.querySelectorAll('.setup-tab-button');
            setupTabButtons.forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600', 'font-medium');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected setup tab content
            document.getElementById(tabName).style.display = 'block';

            // Add active class to clicked setup tab button
            event.target.classList.remove('border-transparent', 'text-gray-500');
            event.target.classList.add('border-blue-500', 'text-blue-600', 'font-medium');
        }

        // Mockup tab switching functionality
        function showMockupTab(tabName) {
            // Hide all mockup tab contents
            const mockupTabs = document.querySelectorAll('.mockup-tab');
            mockupTabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Remove active class from all mockup tab buttons
            const mockupButtons = document.querySelectorAll('.mockup-tab-button');
            mockupButtons.forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected mockup tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked mockup tab button
            event.target.classList.remove('border-transparent', 'text-gray-500');
            event.target.classList.add('border-blue-500', 'text-blue-600');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Any initialization code can go here
            console.log('DataEcho Technical Documentation loaded');
        });
    </script>
</body>
</html>
