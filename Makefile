.PHONY: help install install-dev format lint type-check test test-cov run-dev run-prod docker-build docker-up docker-down clean

# Default target
help: ## Show this help message
	@echo "DataEcho Development Commands"
	@echo "============================="
	@awk 'BEGIN {FS = ":.*##"} /^[a-zA-Z_-]+:.*##/ { printf "  %-20s %s\n", $$1, $$2 }' $(MAKEFILE_LIST)

# Installation
install: ## Install production dependencies with uv
	uv pip install -e .

install-dev: ## Install development dependencies with uv
	uv pip install -e ".[dev]"
	pre-commit install

# Code quality
format: ## Format code with black and ruff
	ruff format .
	ruff check . --fix

lint: ## Lint code with ruff
	ruff check .

type-check: ## Run type checking with mypy
	mypy src/dataecho/ backend/

security-check: ## Run security checks
	bandit -r src/dataecho/ backend/ -c pyproject.toml
	safety check

# Testing
test: ## Run tests
	pytest

test-cov: ## Run tests with coverage report
	pytest --cov=src/dataecho --cov=backend --cov-report=html --cov-report=term-missing

test-watch: ## Run tests in watch mode
	pytest --watch

# Development server
run-dev: ## Run development server with hot reload
	uvicorn backend.api_service.main:app --reload --host 0.0.0.0 --port 8000

run-prod: ## Run production server
	uvicorn backend.api_service.main:app --host 0.0.0.0 --port 8000 --workers 4

# Docker commands
docker-build: ## Build Docker images
	docker-compose build

docker-up: ## Start services with Docker Compose
	docker-compose up -d

docker-up-build: ## Build and start services with Docker Compose
	docker-compose up --build -d

docker-down: ## Stop Docker services
	docker-compose down

docker-logs: ## View Docker logs
	docker-compose logs -f

docker-clean: ## Clean Docker containers and images
	docker-compose down -v --remove-orphans
	docker system prune -f

# Database
db-reset: ## Reset database (when implemented)
	@echo "Database reset not implemented yet"

# Environment setup
setup-env: ## Create .env file from example
	@if [ ! -f .env ]; then \
		echo "Creating .env file from template..."; \
		echo "# DataEcho Environment Configuration" > .env; \
		echo "OPENROUTER_API_KEY=your_openrouter_key_here" >> .env; \
		echo "OPENAI_API_KEY=your_openai_key_here" >> .env; \
		echo "DEFAULT_MODEL=deepseek/deepseek-prover-v2:free" >> .env; \
		echo "UPLOAD_DIR=uploads" >> .env; \
		echo "SESSION_STORAGE_DIR=sessions" >> .env; \
		echo "# Docker Hub credentials for deployment" >> .env; \
		echo "DOCKERHUB_PAT=your_dockerhub_token_here" >> .env; \
		echo "DOCKER_USERNAME=your_dockerhub_username" >> .env; \
		echo ".env file created. Please update it with your actual values."; \
	else \
		echo ".env file already exists"; \
	fi

# Cleanup
clean: ## Clean up build artifacts and cache
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".mypy_cache" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name ".ruff_cache" -exec rm -rf {} + 2>/dev/null || true
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf dist/
	rm -rf build/

# Pre-commit
pre-commit-install: ## Install pre-commit hooks
	pre-commit install

pre-commit-update: ## Update pre-commit hooks
	pre-commit autoupdate

pre-commit-run: ## Run pre-commit on all files
	pre-commit run --all-files

# Documentation (placeholder for future)
docs-build: ## Build documentation
	@echo "Documentation build not implemented yet"

docs-serve: ## Serve documentation locally
	@echo "Documentation serve not implemented yet"

# Quick development setup
dev-setup: install-dev setup-env ## Complete development environment setup (pre-commit hooks optional)
	@echo "Development environment setup complete!"
	@echo "Next steps:"
	@echo "1. Update .env file with your API keys"
	@echo "2. Run 'make run-dev' to start the development server"
	@echo "3. Visit http://localhost:8000/docs for API documentation"

# CI/CD helpers
ci-install: ## Install dependencies for CI
	uv pip install -e ".[dev]"

ci-check: lint type-check security-check test ## Run all CI checks

# Project info
info: ## Show project information
	@echo "DataEcho - AI-Powered Multi-Agent Synthetic Data Generation"
	@echo "Python version: $$(python --version)"
	@echo "UV version: $$(uv --version 2>/dev/null || echo 'UV not installed')"
	@echo "Project root: $$(pwd)"