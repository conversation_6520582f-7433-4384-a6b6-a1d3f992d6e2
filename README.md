# DataEcho - AI-Powered Multi-Agent Synthetic Data Generation

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115+-green.svg)](https://fastapi.tiangolo.com/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Linting: Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)

Industry-grade FastAPI application for data profiling, dependency analysis, and synthetic data generation using multiple AI agents. Developed by Publicis Sapient as an ambitious entry into the synthetic data generation market.

## 🚀 Quick Start

### Prerequisites
- Python 3.12+ 
- [UV](https://docs.astral.sh/uv/) (recommended) or pip
- Git
- Docker & Docker Compose (optional)
- Copy the .env.example file as .env file and put in the OpenRouter API Key

### Windows Setup

```bash
# Complete development environment setup with virtual environment
scripts\dev-setup-venv.bat

# Start development server
scripts\run-dev-venv.bat

# Test the API (optional)
scripts\quick-test.bat

# Activate Python virtual env in subsequent sessions
venv\Scripts\activate.bat
```

### Linux/macOS Setup
```bash
# Complete development environment setup
make dev-setup

# Start development server
make run-dev

# Test the API (optional)
scripts/quick-test.sh
```

### Manual Setup (All Platforms)
```bash
# 1. Clone the repository
git clone <repository-url>
cd data-echo

# 2. Install dependencies
uv pip install -e ".[dev]"

# 3. Setup environment
cp .env.example .env  # Linux/macOS
copy .env.example .env  # Windows

# 4. Install pre-commit hooks (optional - may cause setup issues)
# Only install if you need code quality checks before commits
# pre-commit install

# 5. Run the application
uvicorn backend.api_service.main:app --reload --host 0.0.0.0 --port 8000
```

## ⚡ Quick Test with Sample Data

### Automated Testing
```batch
# Windows - Run automated test script
scripts\quick-test.bat
```

```bash
# Linux/macOS - Run automated test script
scripts/quick-test.sh
```

### Manual Testing via Web Interface
1. Start the development server (see Quick Start above)
2. Visit http://localhost:8000/docs for interactive API documentation
3. Use the `/api/v1/run-complete-workflow` endpoint with the provided `sample_trading_data.csv`

### Manual Testing via Command Line
```bash
# Linux/macOS - Test with curl
curl -X POST "http://localhost:8000/api/v1/run-complete-workflow" \
  -F "domain=trading_data" \
  -F "user_context=Trading data with price, volume, and market indicators" \
  -F "n_rows=25" \
  -F "batch_size=5" \
  -F "file=@sample_trading_data.csv" \
  --output generated_synthetic_data.csv
```

### Using the Web Interface
1. Go to http://localhost:8000/docs
2. Use the `/api/v1/sessions` endpoint to create a session
3. Upload your CSV file via `/api/v1/sessions/{session_id}/upload-csv`
4. Start generation with `/api/v1/sessions/{session_id}/generate`
5. Monitor progress with WebSocket connection to `/ws/{session_id}`
6. Download results from `/api/v1/sessions/{session_id}/download`

## 🏗️ Architecture

DataEcho uses a modern **multi-agent architecture** with three specialized AI agents working sequentially:

```
src/dataecho/                 # Core Python package
├── agents/                   # AI agent implementations
│   ├── base_agent.py        # Base agent class with common functionality
│   ├── profiler_agent.py    # Data structure and pattern analysis
│   ├── dependency_agent.py  # Relationship and constraint identification
│   └── generator_agent.py   # Synthetic data generation
├── orchestrator/             # Workflow coordination
│   └── workflow_orchestrator.py
├── models/                   # Pydantic data models
│   ├── requests.py          # API request models
│   ├── responses.py         # API response models
│   └── agents.py            # Agent state models
├── services/                 # Business logic services
│   ├── session_manager.py   # Session lifecycle management
│   └── llm_service.py       # LLM communication
├── storage/                  # Data persistence layer
│   └── storage_manager.py   # File-based storage (MongoDB planned)
├── websocket/                # Real-time communication
│   └── connection_manager.py
└── utils/                    # Shared utilities
    ├── logger.py            # Structured logging
    └── data_utils.py        # Data processing helpers

backend/                      # Service layer
├── api_service/             # FastAPI HTTP + WebSocket API
│   ├── routers/            # Modular API endpoints
│   │   ├── sessions.py     # Session management
│   │   ├── generation.py   # Data generation workflows
│   │   ├── websockets.py   # Real-time updates
│   │   └── health.py       # Health checks
│   ├── main.py             # Application factory
│   └── Dockerfile
└── agent_service/           # Distributed processing (future)
    └── worker.py

tests/                        # Comprehensive test suite
├── unit/                    # Unit tests for components
├── integration/             # API and workflow tests
└── fixtures/                # Test data and utilities
```

### Workflow Process
1. **ProfilerAgent**: Analyzes CSV structure, data types, patterns, and statistical characteristics
2. **DependencyAgent**: Identifies relationships, constraints, and business rules between fields
3. **GeneratorAgent**: Creates synthetic data maintaining statistical fidelity and dependencies

## 🛠️ Development Commands

### Windows Commands
| Batch Script | Description |
|-------------|-------------|
| `scripts\dev-setup-venv.bat` | Complete development environment setup with virtual environment |
| `scripts\run-dev-venv.bat` | Start development server with virtual environment |
| `scripts\quick-test.bat` | Run automated API test with sample data |
| `scripts\test.bat` | Run test suite |
| `scripts\lint.bat` | Run code linting and type checking |
| `scripts\format.bat` | Format code with Black and Ruff |

### Linux/macOS Commands  
| Command | Description |
|---------|-------------|
| `make help` | Show all available commands |
| `make dev-setup` | Complete development environment setup |
| `make run-dev` | Start development server with hot reload |
| `make test` | Run test suite |
| `make test-cov` | Run tests with coverage report |
| `make lint` | Run code linting with Ruff |
| `make format` | Format code with Black and Ruff |
| `make type-check` | Run type checking with MyPy |
| `make security-check` | Run security scans (Bandit, Safety) |
| `make ci-check` | Run all CI checks (lint, type, security, test) |

### Docker Commands
| Command | Description |
|---------|-------------|
| `make docker-up` | Start services with Docker Compose |
| `make docker-up-build` | Build and start services |
| `make docker-logs` | View container logs |
| `make docker-clean` | Clean containers and images |

## ⚙️ Configuration

### Required Environment Variables
Create a `.env` file (use `.env.example` as template):

```bash
# LLM Service (required - get one of these)
OPENROUTER_API_KEY=your_openrouter_key_here
OPENAI_API_KEY=your_openai_key_here (optional)

# Model Configuration
DEFAULT_MODEL=google/gemini-2.5-flash
MODEL_TEMPERATURE=1.0

# Application Settings
UPLOAD_DIR=uploads
SESSION_STORAGE_DIR=sessions
MAX_FILE_SIZE=10485760  # 10MB

# Development
DEBUG=true
LOG_LEVEL=INFO
```

### Getting API Keys
- **OpenRouter**: Sign up at [openrouter.ai](https://openrouter.ai) for access to multiple LLM models
- **OpenAI**: Get API key from [platform.openai.com](https://platform.openai.com)

## 🧪 Testing

```bash
# Run all tests
make test

# Run with coverage
make test-cov

# Run specific test file
pytest tests/unit/test_agents/test_base_agent.py -v

# Run integration tests only
pytest tests/integration/ -v

# Watch mode for development
make test-watch
```

## 📊 Features

### Current Features (MVP1)
- ✅ **Multi-Agent Pipeline**: Sequential 3-agent workflow
- ✅ **Real-time Updates**: WebSocket progress tracking
- ✅ **Session Management**: Persistent session handling
- ✅ **CSV Processing**: Upload and analysis of existing data
- ✅ **RESTful API**: Complete API with auto-documentation
- ✅ **Modern Tooling**: Python 3.12+, UV, Ruff, MyPy, pre-commit hooks (optional)

### Planned Features (MVP2)
- 🔄 **LangGraph Integration**: Sophisticated agent orchestration
- 🔄 **Parallel Processing**: Concurrent agent execution
- 🔄 **Enhanced Validation**: Advanced quality metrics
- 🔄 **Web UI Foundation**: Basic frontend interface

### Future Features (MVP3)
- 🔮 **RAG Integration**: Real-time domain knowledge
- 🔮 **Advanced Agents**: 15+ specialized agents
- 🔮 **Professional UI**: Conversational data design interface
- 🔮 **Multi-modal Input**: Support for various data formats

## 🔧 API Endpoints

### Session Management
- `POST /api/v1/sessions` - Create new session
- `GET /api/v1/sessions/{session_id}/status` - Get session status
- `DELETE /api/v1/sessions/{session_id}` - Delete session

### Data Processing
- `POST /api/v1/sessions/{session_id}/upload-csv` - Upload CSV file
- `POST /api/v1/sessions/{session_id}/generate` - Start generation
- `GET /api/v1/sessions/{session_id}/results` - Get results
- `GET /api/v1/sessions/{session_id}/download` - Download CSV

### Utilities
- `POST /api/v1/run-complete-workflow` - One-shot workflow for testing
- `GET /health` - Service health check
- `WS /ws/{session_id}` - WebSocket for real-time updates

### Interactive Documentation
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🚀 Production Deployment

### Docker Deployment
```bash
# Production-ready deployment
docker-compose up --build -d

# Check services
docker-compose logs -f
```

### Environment Considerations
- Set appropriate CORS origins in production
- Use production-grade WSGI server (built into Docker)
- Configure proper logging levels
- Set up monitoring and health checks
- Use secure API key management

## 🤝 Contributing

1. **Setup Development Environment**:
   ```bash
   make dev-setup
   ```

2. **Make Changes**: 
   - Follow code style (use `make format` and `make lint` manually)
   - Add tests for new functionality
   - Update documentation as needed
   - Optional: Install pre-commit hooks with `pre-commit install` for automatic checks

3. **Run Quality Checks**:
   ```bash
   make ci-check  # Runs linting, type checking, security, and tests
   ```

4. **Submit PR**: 
   - All checks must pass
   - Include test coverage for new code

## 📈 Performance & Scaling

- **Target Performance**: Generate 50K records in <15 minutes
- **Quality Metrics**: >94% statistical fidelity, >98% constraint compliance
- **Current Architecture**: Sequential processing (MVP1)
- **Future Architecture**: Parallel agent processing with LangGraph (MVP2)

## 🔒 Security

- Environment-based configuration (no hardcoded secrets)
- Input validation and sanitization
- Comprehensive error handling
- Security scanning with Bandit
- Dependency vulnerability checking with Safety
