
# File: README.md
# Multi-Agent Data Generation API

Industry-grade FastAPI application for data profiling, dependency analysis, and synthetic data generation using multiple AI agents.

## Features

- **Multi-Agent Architecture**: Modular design with specialized agents
- **Real-time Updates**: WebSocket support for live progress tracking
- **Session Management**: End-to-end session handling
- **Scalable Design**: Easy to add new agents (RAG, etc.)
- **Database Ready**: Abstracted storage layer for future MongoDB integration
- **Industry Grade**: Proper error handling, logging, and monitoring

## Architecture

```
app/
├── agents/              # AI agent implementations
│   ├── base_agent.py   # Base agent class
│   ├── profiler_agent.py    # Data profiling agent
│   ├── dependency_agent.py  # Dependency analysis agent
│   └── generator_agent.py   # Synthetic data generation agent
├── orchestrator/        # Workflow orchestration
│   └── workflow_orchestrator.py
├── models/             # Pydantic models
│   ├── requests.py     # Request models
│   ├── responses.py    # Response models
│   └── agents.py       # Agent models
├── services/           # Business logic
│   ├── session_manager.py
│   └── llm_service.py
├── storage/            # Data persistence
│   └── storage_manager.py
├── websocket/          # Real-time communication
│   └── connection_manager.py
├── utils/              # Utilities
│   ├── logger.py
│   └── data_utils.py
└── main.py            # FastAPI application
```

## Setup

1. **Clone and Install**:
```bash
# Copy the files to your project directory
pip install -r requirements.txt
```

2. **Environment Setup**:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. **Run the Application**:
```bash
# Using the start script
chmod +x start.sh
./start.sh

# Or directly
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

4. **Using Docker**:
```bash
docker-compose up --build
```

## API Endpoints

### Session Management
- `POST /api/v1/sessions` - Create new session
- `GET /api/v1/sessions/{session_id}/status` - Get session status
- `DELETE /api/v1/sessions/{session_id}` - Delete session

### Data Processing
- `POST /api/v1/sessions/{session_id}/upload-csv` - Upload CSV file
- `POST /api/v1/sessions/{session_id}/generate` - Start data generation
- `GET /api/v1/sessions/{session_id}/results` - Get results

### Real-time Updates
- `WS /ws/{session_id}` - WebSocket for live updates

## Usage Example

1. **Create Session**:
```python
import requests

response = requests.post("http://localhost:8000/api/v1/sessions", json={
    "domain": "financial service trading",
    "user_context": "Trading data with timestamps and volumes"
})
session_id = response.json()["session_id"]
```

2. **Upload CSV**:
```python
with open("your_data.csv", "rb") as f:
    files = {"file": f}
    requests.post(f"http://localhost:8000/api/v1/sessions/{session_id}/upload-csv", files=files)
```

3. **Start Generation**:
```python
requests.post(f"http://localhost:8000/api/v1/sessions/{session_id}/generate", json={
    "n_rows": 100,
    "batch_size": 20
})
```

4. **Monitor Progress** (WebSocket):
```javascript
const ws = new WebSocket(`ws://localhost:8000/ws/${sessionId}`);
ws.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log("Progress:", data);
};
```

## Workflow Steps

1. **Profiler Agent**: Analyzes CSV structure, patterns, and characteristics
2. **Dependency Agent**: Identifies relationships and constraints between fields  
3. **Generator Agent**: Creates synthetic data maintaining patterns and dependencies

## Adding New Agents

1. **Create Agent Class**:
```python
from app.agents.base_agent import BaseAgent
from app.models.agents import AgentType

class MyNewAgent(BaseAgent):
    def __init__(self, session_id: str):
        super().__init__(AgentType.MY_NEW_AGENT, session_id)
    
    async def process(self, input_data):
        # Your agent logic here
        return {"result": "processed"}
```

2. **Update Orchestrator**:
```python
# Add to workflow_orchestrator.py
await self._run_my_new_agent_step(input_data)
```

## WebSocket Message Types

- `status_update`: General status updates
- `progress`: Progress tracking with percentages
- `completion`: Workflow completion notification
- `error`: Error notifications

## Future Enhancements

- **MongoDB Integration**: Replace file storage with MongoDB
- **RAG Agent**: Add retrieval-augmented generation
- **Caching Layer**: Redis for session caching
- **Monitoring**: Prometheus metrics and health checks
- **Authentication**: JWT-based user authentication

## Configuration

Key settings in `.env`:
- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `DEFAULT_MODEL`: AI model to use
- `UPLOAD_DIR`: Directory for uploaded files
- `SESSION_STORAGE_DIR`: Directory for session data

## Error Handling

- Comprehensive error catching and logging
- Graceful fallbacks for failed operations
- Detailed error messages via WebSocket
- Retry mechanisms for transient failures

## Production Deployment

1. Set appropriate CORS origins
2. Configure proper logging levels
3. Set up reverse proxy (Nginx)
4. Use production WSGI server (Gunicorn)
5. Set up monitoring and health checks

## API Documentation

Once running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`