# DataEcho - AI-Powered Multi-Agent Synthetic Data Generation

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115+-green.svg)](https://fastapi.tiangolo.com/)
[![Next.js 15](https://img.shields.io/badge/Next.js-15-black.svg)](https://nextjs.org/)
[![React 19](https://img.shields.io/badge/React-19-blue.svg)](https://reactjs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-16+-blue.svg)](https://www.postgresql.org/)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Linting: Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/charliermarsh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)

Industry-grade full-stack application for data profiling, dependency analysis, and synthetic data generation using multiple AI agents. Features a beautiful modern web interface with real-time progress tracking, comprehensive database integration, and production-ready Docker deployment. Developed by Publicis Sapient as an ambitious entry into the synthetic data generation market.

## 🚀 Quick Start

### Prerequisites
- Python 3.12+
- Node.js 18+ (for frontend)
- [UV](https://docs.astral.sh/uv/) (recommended) or pip
- Git
- PostgreSQL 16+ (or use Docker)
- Docker & Docker Compose (recommended for full-stack deployment)
- OpenRouter API Key (for AI model access)

### 🏗️ Developer Setup (Local Development)

#### 1. Clone and Setup Environment

```bash
# Clone the repository
git clone <repository-url>
cd data-echo

# Copy environment configuration
cp .env.example .env
# Edit .env and add your OpenRouter API key:
# OPENROUTER_API_KEY=your_api_key_here
```

#### 2. Database Setup

**Option A: PostgreSQL with Docker (Recommended)**
```bash
# Start PostgreSQL container
docker run --name dataecho-postgres \
  -e POSTGRES_DB=dataecho \
  -e POSTGRES_USER=dataecho \
  -e POSTGRES_PASSWORD=dataecho123 \
  -p 5432:5432 \
  -d postgres:16

# Database will be available at: postgresql://dataecho:dataecho123@localhost:5432/dataecho
```

**Option B: Local PostgreSQL Installation**
```bash
# Install PostgreSQL 16+ on your system
# Create database and user:
createdb dataecho
createuser -P dataecho  # Set password: dataecho123

# Update .env with your database credentials
```

#### 3. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies with UV (recommended)
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uv pip install -r requirements.txt

# Or with pip
pip install -r requirements.txt

# Run database migrations (creates tables automatically)
cd api_service
alembic upgrade head

# Start backend development server (from project root)
cd ../..
python -m uvicorn backend.api_service.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 4. Frontend Setup

```bash
# In a new terminal, navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start frontend development server
npm run dev
```

#### 5. Access the Application

- **Frontend**: http://localhost:3000 (or http://localhost:3001 if 3000 is in use)
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: postgresql://dataecho:dataecho123@localhost:5432/dataecho

### 🐳 Full-Stack Docker Setup (Production-like)

```bash
# Start all services with Docker Compose
docker-compose up --build

# Access the application:
# - Frontend: http://localhost:3000
# - Backend API: http://localhost:8000
# - API Documentation: http://localhost:8000/docs
```

### 🔧 Database Migrations with Alembic

DataEcho uses **Alembic** for database schema management and migrations. Alembic is a lightweight database migration tool for SQLAlchemy, providing:

- **Version Control for Database Schema**: Track changes to your database structure over time
- **Automatic Migration Generation**: Generate migration scripts from SQLAlchemy model changes
- **Safe Schema Updates**: Apply database changes incrementally and safely
- **Rollback Capability**: Revert database changes if needed

#### Common Alembic Commands

```bash
# Navigate to the API service directory
cd backend/api_service

# Check current migration status
alembic current

# Apply all pending migrations (run this after setup)
alembic upgrade head

# Generate new migration after model changes
alembic revision --autogenerate -m "Description of changes"

# Apply specific migration
alembic upgrade <revision_id>

# Rollback to previous migration
alembic downgrade -1

# View migration history
alembic history
```

#### Database Schema Initialization

The database tables are created automatically when you run:
```bash
cd backend/api_service
alembic upgrade head
```

This creates all necessary tables including:
- `sessions` - Session management and metadata
- `agents` - Agent status and progress tracking
- `results` - Generated data and analysis results
- Other supporting tables for the application

### Windows Setup (Development)
scripts\quick-test.bat

# Activate Python virtual env in subsequent sessions
venv\Scripts\activate.bat
```

### Linux/macOS Setup (Development)
```bash
# Complete development environment setup
make dev-setup

# Start backend development server
make run-dev

# In a separate terminal, start frontend
cd frontend
npm install
npm run dev

# Test the API (optional)
scripts/quick-test.sh
```

### Manual Setup (All Platforms)
```bash
# 1. Clone the repository
git clone <repository-url>
cd data-echo

# 2. Install dependencies
uv pip install -e ".[dev]"

# 3. Setup environment
cp .env.example .env  # Linux/macOS
copy .env.example .env  # Windows

# 4. Install pre-commit hooks (optional - may cause setup issues)
# Only install if you need code quality checks before commits
# pre-commit install

# 5. Run the application
uvicorn backend.api_service.main:app --reload --host 0.0.0.0 --port 8000
```

## ⚡ Quick Test with Sample Data

### Automated Testing
```batch
# Windows - Run automated test script
scripts\quick-test.bat
```

```bash
# Linux/macOS - Run automated test script
scripts/quick-test.sh
```

### Web Interface Testing (Recommended)
1. Start the full-stack application: `docker-compose up --build`
2. Visit http://localhost:3000 for the beautiful web interface
3. Upload a CSV file, configure generation parameters, and watch real-time progress
4. Download results and view comprehensive quality metrics

### API Testing via Swagger UI
1. Start the development server (see Quick Start above)
2. Visit http://localhost:8000/docs for interactive API documentation
3. Use the `/api/v1/run-complete-workflow` endpoint with the provided `sample_trading_data.csv`

### Manual Testing via Command Line
```bash
# Linux/macOS - Test with curl
curl -X POST "http://localhost:8000/api/v1/run-complete-workflow" \
  -F "domain=trading_data" \
  -F "user_context=Trading data with price, volume, and market indicators" \
  -F "n_rows=25" \
  -F "batch_size=5" \
  -F "file=@sample_trading_data.csv" \
  --output generated_synthetic_data.csv
```

### Using the Modern Web Interface
1. Go to http://localhost:3000 (frontend) or http://localhost:8000/docs (API docs)
2. **Web UI Flow**:
   - Upload CSV file with drag-and-drop interface
   - Configure generation parameters (rows, batch size)
   - Watch real-time agent progress with beautiful animations
   - View comprehensive results with data preview and quality metrics
   - Download generated data with one click
   - Start new sessions seamlessly

3. **API Flow** (for developers):
   - Create session via `/api/v1/sessions`
   - Upload CSV via `/api/v1/sessions/{session_id}/upload-csv`
   - Start generation with `/api/v1/sessions/{session_id}/generate`
   - Monitor progress with WebSocket connection to `/ws/{session_id}`
   - Download results from `/api/v1/sessions/{session_id}/download`

## 🏗️ Architecture

DataEcho uses a modern **full-stack multi-agent architecture** with three specialized AI agents, real-time WebSocket communication, PostgreSQL database integration, and a beautiful React frontend:

```
src/dataecho/                 # Core Python package
├── agents/                   # AI agent implementations
│   ├── base_agent.py        # Base agent class with common functionality
│   ├── profiler_agent.py    # Data structure and pattern analysis
│   ├── dependency_agent.py  # Relationship and constraint identification
│   └── generator_agent.py   # Synthetic data generation
├── orchestrator/             # Workflow coordination
│   └── workflow_orchestrator.py
├── models/                   # Pydantic data models
│   ├── requests.py          # API request models
│   ├── responses.py         # API response models
│   └── agents.py            # Agent state models
├── services/                 # Business logic services
│   ├── session_manager.py   # Database-backed session management
│   └── llm_service.py       # LLM communication with token tracking
├── database/                 # Database layer (NEW)
│   ├── models.py            # SQLAlchemy models
│   ├── service.py           # Database service layer
│   └── config.py            # Database configuration
├── websocket/                # Real-time communication
│   └── connection_manager.py # Singleton WebSocket manager
└── utils/                    # Shared utilities
    ├── logger.py            # Structured logging
    └── data_utils.py        # Data processing helpers

backend/                      # Service layer
├── api_service/             # FastAPI HTTP + WebSocket API
│   ├── routers/            # Modular API endpoints
│   │   ├── sessions.py     # Session management
│   │   ├── generation.py   # Data generation workflows
│   │   ├── websockets.py   # Real-time updates
│   │   ├── tokens.py       # Token usage tracking (NEW)
│   │   └── health.py       # Health checks
│   ├── main.py             # Application factory
│   └── Dockerfile          # Production-ready container

frontend/                     # Modern React Frontend (NEW)
├── src/
│   ├── app/                # Next.js 15 App Router
│   ├── components/         # Reusable UI components
│   │   ├── ui/            # Shadcn/ui components
│   │   └── features/      # Feature-specific components
│   ├── hooks/             # Custom React hooks
│   ├── services/          # API service layer
│   ├── types/             # TypeScript definitions
│   └── utils/             # Frontend utilities
├── public/                # Static assets
├── tailwind.config.js     # Tailwind CSS configuration
├── next.config.js         # Next.js configuration
├── package.json           # Dependencies
└── Dockerfile             # Production container

tests/                        # Comprehensive test suite
├── unit/                    # Unit tests for components
├── integration/             # API and workflow tests
└── fixtures/                # Test data and utilities
```

### Workflow Process
1. **ProfilerAgent**: Analyzes CSV structure, data types, patterns, and statistical characteristics
2. **DependencyAgent**: Identifies relationships, constraints, and business rules between fields
3. **GeneratorAgent**: Creates synthetic data maintaining statistical fidelity and dependencies

### Technology Stack
**Backend:**
- FastAPI with async/await for high performance
- PostgreSQL with SQLAlchemy ORM for data persistence
- WebSocket for real-time communication
- Alembic for database migrations
- Comprehensive token usage tracking and analytics

**Frontend:**
- Next.js 15 with App Router and React 19
- TypeScript 5.6 for type safety
- Tailwind CSS 3 for styling
- Shadcn/ui for beautiful components
- Motion (Framer Motion) for smooth animations
- Real-time WebSocket integration

**Infrastructure:**
- Docker & Docker Compose for containerization
- Multi-stage builds for optimized production images
- Health checks and proper networking
- Environment-based configuration

## 🛠️ Development Commands

### Windows Commands
| Batch Script | Description |
|-------------|-------------|
| `scripts\dev-setup-venv.bat` | Complete development environment setup with virtual environment |
| `scripts\run-dev-venv.bat` | Start development server with virtual environment |
| `scripts\quick-test.bat` | Run automated API test with sample data |
| `scripts\test.bat` | Run test suite |
| `scripts\lint.bat` | Run code linting and type checking |
| `scripts\format.bat` | Format code with Black and Ruff |

### Linux/macOS Commands  
| Command | Description |
|---------|-------------|
| `make help` | Show all available commands |
| `make dev-setup` | Complete development environment setup |
| `make run-dev` | Start development server with hot reload |
| `make test` | Run test suite |
| `make test-cov` | Run tests with coverage report |
| `make lint` | Run code linting with Ruff |
| `make format` | Format code with Black and Ruff |
| `make type-check` | Run type checking with MyPy |
| `make security-check` | Run security scans (Bandit, Safety) |
| `make ci-check` | Run all CI checks (lint, type, security, test) |

### Docker Commands
| Command | Description |
|---------|-------------|
| `make docker-up` | Start services with Docker Compose |
| `make docker-up-build` | Build and start services |
| `make docker-logs` | View container logs |
| `make docker-clean` | Clean containers and images |

## ⚙️ Configuration

### Required Environment Variables
Create a `.env` file (use `.env.example` as template):

```bash
# LLM Service (required - get one of these)
OPENROUTER_API_KEY=your_openrouter_key_here
OPENAI_API_KEY=your_openai_key_here (optional)

# Model Configuration
DEFAULT_MODEL=google/gemini-2.5-flash
MODEL_TEMPERATURE=1.0

# Database Configuration (NEW)
DATABASE_URL=postgresql://dataecho:password@localhost:5432/dataecho
POSTGRES_USER=dataecho
POSTGRES_PASSWORD=password
POSTGRES_DB=dataecho

# Application Settings
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# Development
DEBUG=true
LOG_LEVEL=INFO

# Frontend Configuration (for development)
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Getting API Keys
- **OpenRouter**: Sign up at [openrouter.ai](https://openrouter.ai) for access to multiple LLM models
- **OpenAI**: Get API key from [platform.openai.com](https://platform.openai.com)

## 🧪 Testing

```bash
# Run all tests
make test

# Run with coverage
make test-cov

# Run specific test file
pytest tests/unit/test_agents/test_base_agent.py -v

# Run integration tests only
pytest tests/integration/ -v

# Watch mode for development
make test-watch
```

## 📊 Features

### Current Features (MVP2 - COMPLETED)
- ✅ **Beautiful Web Interface**: Modern React frontend with drag-and-drop file upload
- ✅ **Real-time Progress Tracking**: Live agent status updates with animations
- ✅ **Database Integration**: PostgreSQL with comprehensive session and token tracking
- ✅ **Multi-Agent Pipeline**: Sequential 3-agent workflow with individual token usage
- ✅ **WebSocket Communication**: Real-time bidirectional communication
- ✅ **Session Management**: Database-backed persistent session handling
- ✅ **CSV Processing**: Upload, analysis, and generation of synthetic data
- ✅ **Quality Metrics**: Comprehensive data quality analysis and reporting
- ✅ **Token Usage Analytics**: Detailed tracking by agent with cost estimation
- ✅ **Data Preview**: Interactive preview of generated data with download options
- ✅ **Responsive Design**: Mobile-friendly interface with micro-animations
- ✅ **Docker Deployment**: Production-ready containerized deployment
- ✅ **RESTful API**: Complete API with auto-documentation
- ✅ **Modern Tooling**: Python 3.12+, Next.js 15, TypeScript, Tailwind CSS

### Planned Features (MVP3)
- 🔄 **LangGraph Integration**: Sophisticated agent orchestration with parallel execution
- 🔄 **Advanced Analytics**: Enhanced quality metrics and data insights
- 🔄 **Multi-format Support**: Excel, JSON, Parquet file support
- 🔄 **Batch Processing**: Handle multiple files simultaneously

### Future Features (MVP4)
- 🔮 **RAG Integration**: Real-time domain knowledge and context awareness
- 🔮 **Advanced Agents**: 15+ specialized agents for different data types
- 🔮 **Conversational Interface**: AI-powered data design conversations
- 🔮 **Multi-modal Input**: Support for images, documents, and APIs
- 🔮 **Enterprise Features**: User management, audit logs, compliance reporting

## 🔧 API Endpoints

### Session Management
- `POST /api/v1/sessions` - Create new session
- `GET /api/v1/sessions/{session_id}/status` - Get session status
- `DELETE /api/v1/sessions/{session_id}` - Delete session

### Data Processing
- `POST /api/v1/sessions/{session_id}/upload-csv` - Upload CSV file
- `POST /api/v1/sessions/{session_id}/generate` - Start generation (non-blocking)
- `GET /api/v1/sessions/{session_id}/results` - Get generation results
- `GET /api/v1/sessions/{session_id}/preview` - Preview generated data
- `GET /api/v1/sessions/{session_id}/quality` - Get quality metrics
- `GET /api/v1/sessions/{session_id}/download` - Download CSV

### Token Usage & Analytics
- `GET /api/v1/sessions/{session_id}/tokens` - Get session token usage
- `GET /api/v1/tokens/total` - Get total token usage across all sessions
- `GET /api/v1/tokens/analytics` - Get detailed token analytics

### Real-time Communication
- `WS /api/v1/ws/{session_id}` - WebSocket for real-time updates

### Utilities
- `POST /api/v1/run-complete-workflow` - One-shot workflow for testing
- `GET /health` - Service health check

### Interactive Documentation
- **Web Interface**: http://localhost:3000 (Primary user interface)
- **Swagger UI**: http://localhost:8000/docs (API documentation)
- **ReDoc**: http://localhost:8000/redoc (Alternative API docs)

## 🚀 Production Deployment

### Docker Deployment (Recommended)
```bash
# Production-ready full-stack deployment
docker-compose up --build -d

# Check all services
docker-compose logs -f

# Scale specific services if needed
docker-compose up --scale backend=2 -d
```

### Services Overview
- **Frontend**: Next.js production build on port 3000
- **Backend**: FastAPI with Uvicorn on port 8000
- **Database**: PostgreSQL 16 with persistent volumes
- **Networking**: Internal Docker network with health checks

### Environment Considerations
- Set appropriate CORS origins in production
- Configure PostgreSQL with proper credentials
- Use production-grade logging levels
- Set up monitoring and health checks
- Use secure API key management
- Configure SSL/TLS for production domains

## 🤝 Contributing

1. **Setup Development Environment**:
   ```bash
   make dev-setup
   ```

2. **Make Changes**: 
   - Follow code style (use `make format` and `make lint` manually)
   - Add tests for new functionality
   - Update documentation as needed
   - Optional: Install pre-commit hooks with `pre-commit install` for automatic checks

3. **Run Quality Checks**:
   ```bash
   make ci-check  # Runs linting, type checking, security, and tests
   ```

4. **Submit PR**: 
   - All checks must pass
   - Include test coverage for new code

## 📈 Performance & Scaling

- **Target Performance**: Generate 50K records in <15 minutes
- **Quality Metrics**: >94% statistical fidelity, >98% constraint compliance
- **Current Architecture**: Sequential processing (MVP1)
- **Future Architecture**: Parallel agent processing with LangGraph (MVP2)

## 🔒 Security

- Environment-based configuration (no hardcoded secrets)
- Input validation and sanitization
- Comprehensive error handling
- Security scanning with Bandit
- Dependency vulnerability checking with Safety
