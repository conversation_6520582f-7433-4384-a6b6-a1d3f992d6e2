# File: app/agents/profiler_agent.py
import pandas as pd
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from typing import Dict, Any
from app.agents.base_agent import BaseAgent
from app.models.agents import AgentType, AgentConfig
from app.utils.data_utils import extract_and_parse_json
from app.prompts import PROFILER_SYSTEM_PROMPT2, PROFILER_USER_PROMPT2

class ProfilerAgent(BaseAgent):
    """Agent for data profiling"""
    
    def __init__(self, session_id: str):
        super().__init__(AgentType.PROFILER, session_id)
        self.memory = MemorySaver()
    
    def _get_default_config(self) -> AgentConfig:
        return AgentConfig(
            agent_type=AgentType.PROFILER,
            model_name="deepseek/deepseek-prover-v2:free",
            temperature=1.0,
            max_retries=2
        )
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process CSV data and generate profile"""
        csv_file_path = input_data.get("csv_file_path")
        domain = input_data.get("domain", "")
        user_context = input_data.get("user_context", "")
        n_profiles = input_data.get("n_profiles", 5)
        
        # Read CSV data
        df = pd.read_csv(csv_file_path)
        
        # Get basic dataset info
        row_count = len(df)
        column_count = len(df.columns)
        sample_size = min(100, row_count)
        csv_sample = df.head(sample_size).to_csv(index=False)
        
        # Format prompts
        formatted_system_prompt = PROFILER_SYSTEM_PROMPT2.format(DOMAIN=domain)
        formatted_user_prompt = PROFILER_USER_PROMPT2.format(
            N=n_profiles,
            csv_sample=csv_sample,
            user_context=f"{user_context}\n\nThis dataset has {row_count} rows and {column_count} columns."
        )
        
        # Create messages
        input_messages = [
            SystemMessage(content=formatted_system_prompt),
            HumanMessage(content=formatted_user_prompt)
        ]
        
        # Configure LLM
        config = {"configurable": {"thread_id": f"profiling-{self.session_id}"}}
        
        # Get response
        output = await self.llm_service.invoke_with_messages(input_messages, config, self.memory)
        
        # Parse JSON response
        profile_data = extract_and_parse_json(output["messages"][-1].content)
        
        return {
            "profile": profile_data,
            "dataset_info": {
                "row_count": row_count,
                "column_count": column_count,
                "columns": list(df.columns)
            }
        }
