# File: app/config.py
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # OpenAI/OpenRouter Configuration
    openai_api_key: Optional[str] = None
    openrouter_api_key: Optional[str] = None
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    
    # Model Configuration
    default_model: str = "deepseek/deepseek-prover-v2:free"
    model_temperature: float = 1.0
    model_max_tokens: Optional[int] = None
    model_timeout: Optional[int] = None
    model_max_retries: int = 2
    
    # Application Configuration
    upload_dir: str = "uploads"
    session_storage_dir: str = "sessions"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # Database Configuration (for future MongoDB integration)
    mongodb_url: Optional[str] = None
    mongodb_db_name: str = "multiagent_data_gen"
    
    # WebSocket Configuration
    websocket_timeout: int = 300  # 5 minutes
    
    class Config:
        env_file = ".env"

settings = Settings()

