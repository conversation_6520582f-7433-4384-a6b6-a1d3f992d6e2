# File: app/models/requests.py
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any

class SessionRequest(BaseModel):
    domain: str = Field(..., description="Domain for data generation (e.g., 'financial service trading')")
    user_context: Optional[str] = Field("", description="Additional context from user")

class DataGenerationRequest(BaseModel):
    n_rows: int = Field(20, ge=1, le=1000, description="Number of rows to generate")
    batch_size: int = Field(10, ge=1, le=100, description="Batch size for generation")

class AgentRequest(BaseModel):
    session_id: str
    agent_type: str
    parameters: Dict[str, Any] = Field(default_factory=dict)
