

# File: app/models/responses.py
from pydantic import BaseModel
from typing import Optional, Dict, Any, List

class SessionResponse(BaseModel):
    session_id: str
    status: str
    message: str

class StatusResponse(BaseModel):
    session_id: str
    status: str
    current_step: str
    progress: float
    message: str
    data: Dict[str, Any]

class AgentResponse(BaseModel):
    agent_id: str
    agent_type: str
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class WebSocketMessage(BaseModel):
    type: str  # 'status_update', 'progress', 'completion', 'error'
    session_id: str
    data: Dict[str, Any]
