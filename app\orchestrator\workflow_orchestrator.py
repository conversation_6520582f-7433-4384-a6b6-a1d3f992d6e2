
# File: app/orchestrator/workflow_orchestrator.py
import asyncio
from typing import Dict, Any, Optional
from app.agents.profiler_agent import ProfilerAgent
from app.agents.dependency_agent import DependencyAgent
from app.agents.generator_agent import GeneratorAgent
from app.services.session_manager import SessionManager
from app.storage.storage_manager import StorageManager
from app.websocket.connection_manager import ConnectionManager
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class WorkflowOrchestrator:
    """Orchestrates multi-agent workflows"""
    
    def __init__(self, session_id: str, connection_manager: ConnectionManager, storage_manager: StorageManager):
        self.session_id = session_id
        self.connection_manager = connection_manager
        self.storage_manager = storage_manager
        self.session_manager = SessionManager()
    
    async def start_workflow(self, csv_file_path: str, domain: str, user_context: str, 
                           n_rows: int = 20, batch_size: int = 10):
        """Start the complete data generation workflow"""
        try:
            # Update session status
            await self.session_manager.update_session(self.session_id, {
                "status": "running",
                "current_step": "initializing",
                "progress": 0,
                "message": "Starting workflow..."
            })
            
            # Send WebSocket update
            await self.connection_manager.send_status_update(
                self.session_id, "running", "initializing", 0, "Starting workflow..."
            )
            
            # Step 1: Profile the data
            await self._run_profiler_step(csv_file_path, domain, user_context)
            
            # Step 2: Analyze dependencies
            await self._run_dependency_step(user_context)
            
            # Step 3: Generate synthetic data
            await self._run_generator_step(user_context, n_rows, batch_size)
            
            # Complete workflow
            await self._complete_workflow()
            
        except Exception as e:
            await self._handle_workflow_error(str(e))
    
    async def _run_profiler_step(self, csv_file_path: str, domain: str, user_context: str):
        """Run the profiler agent"""
        logger.info(f"Starting profiler step for session: {self.session_id}")
        
        # Update status
        await self.session_manager.update_session(self.session_id, {
            "current_step": "profiling",
            "progress": 10,
            "message": "Analyzing data structure and patterns..."
        })
        
        await self.connection_manager.send_status_update(
            self.session_id, "running", "profiling", 10, "Analyzing data structure and patterns..."
        )
        
        # Create and run profiler agent
        profiler = ProfilerAgent(self.session_id)
        
        input_data = {
            "csv_file_path": csv_file_path,
            "domain": domain,
            "user_context": user_context,
            "n_profiles": 5
        }
        
        profile_result = await profiler.run(input_data)
        
        # Save results
        await self.storage_manager.save_agent_result(self.session_id, "profiler", profile_result)
        
        # Update session with results
        await self.session_manager.update_session(self.session_id, {
            "progress": 30,
            "message": "Data profiling completed",
            "data": {"profile_completed": True}
        })
        
        await self.connection_manager.send_status_update(
            self.session_id, "running", "profiling", 30, "Data profiling completed",
            {"profile_summary": profile_result.get("dataset_info", {})}
        )
        
        logger.info(f"Profiler step completed for session: {self.session_id}")
    
    async def _run_dependency_step(self, user_context: str):
        """Run the dependency analysis agent"""
        logger.info(f"Starting dependency step for session: {self.session_id}")
        
        # Update status
        await self.session_manager.update_session(self.session_id, {
            "current_step": "dependency_analysis",
            "progress": 40,
            "message": "Analyzing data dependencies and relationships..."
        })
        
        await self.connection_manager.send_status_update(
            self.session_id, "running", "dependency_analysis", 40, 
            "Analyzing data dependencies and relationships..."
        )
        
        # Get profiler results
        profile_result = await self.storage_manager.get_agent_result(self.session_id, "profiler")
        if not profile_result:
            raise Exception("Profiler results not found")
        
        # Create and run dependency agent
        dependency_agent = DependencyAgent(self.session_id)
        
        input_data = {
            "profile_data": profile_result.get("profile"),
            "user_context": user_context
        }
        
        dependency_result = await dependency_agent.run(input_data)
        
        # Save results
        await self.storage_manager.save_agent_result(self.session_id, "dependency", dependency_result)
        
        # Update session
        await self.session_manager.update_session(self.session_id, {
            "progress": 60,
            "message": "Dependency analysis completed",
            "data": {"dependency_completed": True}
        })
        
        await self.connection_manager.send_status_update(
            self.session_id, "running", "dependency_analysis", 60, "Dependency analysis completed"
        )
        
        logger.info(f"Dependency step completed for session: {self.session_id}")
    
    async def _run_generator_step(self, user_context: str, n_rows: int, batch_size: int):
        """Run the data generator agent"""
        logger.info(f"Starting generator step for session: {self.session_id}")
        
        # Update status
        await self.session_manager.update_session(self.session_id, {
            "current_step": "generating",
            "progress": 70,
            "message": "Generating synthetic data..."
        })
        
        await self.connection_manager.send_status_update(
            self.session_id, "running", "generating", 70, "Generating synthetic test data..."
        )
        
        # Get previous results
        profile_result = await self.storage_manager.get_agent_result(self.session_id, "profiler")
        dependency_result = await self.storage_manager.get_agent_result(self.session_id, "dependency")
        
        if not profile_result or not dependency_result:
            raise Exception("Previous agent results not found")
        
        # Create and run generator agent
        generator = GeneratorAgent(self.session_id, self.connection_manager)
        
        input_data = {
            "profile_data": profile_result.get("profile"),
            "dependency_data": dependency_result.get("dependencies"),
            "user_context": user_context,
            "n_rows": n_rows,
            "batch_size": batch_size
        }
        
        generation_result = await generator.run(input_data)
        
        # Save results
        await self.storage_manager.save_agent_result(self.session_id, "generator", generation_result)
        
        # Update session
        await self.session_manager.update_session(self.session_id, {
            "progress": 95,
            "message": f"Generated {generation_result.get('total_generated', 0)} rows of synthetic data",
            "data": {"generation_completed": True, "rows_generated": generation_result.get('total_generated', 0)}
        })
        
        await self.connection_manager.send_status_update(
            self.session_id, "running", "generating", 95, 
            f"Generated {generation_result.get('total_generated', 0)} rows of synthetic data",
            {"rows_generated": generation_result.get('total_generated', 0)}
        )
        
        logger.info(f"Generator step completed for session: {self.session_id}")
    
    async def _complete_workflow(self):
        """Complete the workflow"""
        logger.info(f"Completing workflow for session: {self.session_id}")
        
        # Get all results
        results = await self.storage_manager.get_session_results(self.session_id)
        
        # Update session
        await self.session_manager.update_session(self.session_id, {
            "status": "completed",
            "current_step": "completed",
            "progress": 100,
            "message": "Workflow completed successfully",
            "data": {"workflow_completed": True}
        })
        
        # Send completion notification
        await self.connection_manager.send_completion(self.session_id, results)
        
        logger.info(f"Workflow completed for session: {self.session_id}")
    
    async def _handle_workflow_error(self, error_message: str):
        """Handle workflow errors"""
        logger.error(f"Workflow error for session {self.session_id}: {error_message}")
        
        # Update session
        await self.session_manager.update_session(self.session_id, {
            "status": "failed",
            "current_step": "error",
            "message": f"Workflow failed: {error_message}",
            "data": {"error": error_message}
        })
        
        # Send error notification
        await self.connection_manager.send_error(self.session_id, error_message)

