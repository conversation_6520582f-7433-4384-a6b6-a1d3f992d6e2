# File: app/websocket/connection_manager.py
from fastapi import WebSocket
from typing import Dict, List
import json
from app.utils.logger import setup_logger

logger = setup_logger(__name__)

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, session_id: str):
        """Accept WebSocket connection"""
        await websocket.accept()
        self.active_connections[session_id] = websocket
        logger.info(f"WebSocket connected for session: {session_id}")
    
    def disconnect(self, session_id: str):
        """Remove WebSocket connection"""
        if session_id in self.active_connections:
            del self.active_connections[session_id]
            logger.info(f"WebSocket disconnected for session: {session_id}")
    
    async def send_personal_message(self, message: dict, session_id: str):
        """Send message to specific session"""
        if session_id in self.active_connections:
            try:
                await self.active_connections[session_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {session_id}: {str(e)}")
                self.disconnect(session_id)
    
    async def send_status_update(self, session_id: str, status: str, step: str, progress: float, message: str, data: dict = None):
        """Send status update to client"""
        update_message = {
            "type": "status_update",
            "session_id": session_id,
            "data": {
                "status": status,
                "current_step": step,
                "progress": progress,
                "message": message,
                "data": data or {}
            }
        }
        await self.send_personal_message(update_message, session_id)
    
    async def send_progress_update(self, session_id: str, current: int, total: int, message: str):
        """Send progress update"""
        progress = (current / total) * 100 if total > 0 else 0
        progress_message = {
            "type": "progress",
            "session_id": session_id,
            "data": {
                "current": current,
                "total": total,
                "progress": progress,
                "message": message
            }
        }
        await self.send_personal_message(progress_message, session_id)
    
    async def send_completion(self, session_id: str, results: dict):
        """Send completion message"""
        completion_message = {
            "type": "completion",
            "session_id": session_id,
            "data": {
                "message": "Workflow completed successfully",
                "results": results
            }
        }
        await self.send_personal_message(completion_message, session_id)
    
    async def send_error(self, session_id: str, error: str):
        """Send error message"""
        error_message = {
            "type": "error",
            "session_id": session_id,
            "data": {
                "error": error,
                "message": "An error occurred during processing"
            }
        }
        await self.send_personal_message(error_message, session_id)



