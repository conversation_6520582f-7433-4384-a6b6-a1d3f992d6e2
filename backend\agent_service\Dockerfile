# Dockerfile for DataEcho agent-service
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching (shared at repo root)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application source code into the container
COPY . .

# Ensure root repo is on PYTHONPATH so 'app' package is importable
ENV PYTHONPATH=/app

# Default command runs the agent workflow
CMD ["python", "backend/agent_service/worker.py"]
