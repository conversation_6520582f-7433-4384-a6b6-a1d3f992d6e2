# Dockerfile for DataEcho agent-service
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install UV package manager
RUN pip install --no-cache-dir uv

# Copy pyproject.toml for modern dependency management
COPY pyproject.toml ./
RUN uv pip install --system --no-cache .

# Copy application code (excluding virtual environments and dev files)
COPY src/ src/
COPY backend/ backend/

# Ensure src is on PYTHONPATH so 'dataecho' package is importable
ENV PYTHONPATH=/app/src

# Create necessary directories
RUN mkdir -p uploads sessions results

# Default command runs the agent workflow
CMD ["python", "backend/agent_service/worker.py"]
