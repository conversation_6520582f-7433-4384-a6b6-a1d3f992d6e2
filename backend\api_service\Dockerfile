# File: Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Ensure root repo is on PYTHONPATH so 'app' imports resolve
ENV PYTHONPATH=/app

# Create necessary directories
RUN mkdir -p uploads sessions results

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "backend.api_service.main:app", "--host", "0.0.0.0", "--port", "8000"]

