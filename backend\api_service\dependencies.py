"""Shared dependencies for the API service."""

from dataecho.websocket.connection_manager import ConnectionManager
from dataecho.services.session_manager import SessionManager
from dataecho.storage.storage_manager import StorageManager

# Shared instances - single source of truth
connection_manager = ConnectionManager()
session_manager = SessionManager()
storage_manager = StorageManager()

def get_connection_manager() -> ConnectionManager:
    """Get the shared connection manager instance."""
    return connection_manager

def get_session_manager() -> SessionManager:
    """Get the shared session manager instance."""
    return session_manager

def get_storage_manager() -> StorageManager:
    """Get the shared storage manager instance."""
    return storage_manager
