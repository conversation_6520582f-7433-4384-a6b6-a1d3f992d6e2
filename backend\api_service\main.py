"""Main FastAPI application for DataEcho multi-agent system."""

import uvicorn
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from dataecho.storage.storage_manager import StorageManager
from dataecho.utils.logger import setup_logger
from .routers import health, sessions, generation, websockets, tokens

# Setup logging
logger = setup_logger(__name__)

# Global instances
storage_manager = StorageManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler."""
    logger.info("Starting FastAPI Multi-Agent Application")
    # Initialize storage
    await storage_manager.initialize()
    yield
    logger.info("Shutting down application")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="Multi-Agent Data Generation API",
        description="Industry-grade multi-agent system for data profiling, dependency analysis, and synthetic data generation",
        version="1.0.0",
        lifespan=lifespan,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json"
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routers
    app.include_router(health.router)
    app.include_router(sessions.router)
    app.include_router(generation.router)
    app.include_router(websockets.router)
    app.include_router(tokens.router)

    return app


# Create app instance
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "api_service.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )