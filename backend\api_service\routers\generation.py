"""Data generation workflow API routes."""

import os
import json
import csv
import asyncio
from fastapi import APIRouter, HTTPException, BackgroundTasks, Form, UploadFile, File
from fastapi.responses import FileResponse
from dataecho.models.requests import DataGenerationRequest
from dataecho.orchestrator.workflow_orchestrator import WorkflowOrchestrator
from dataecho.services.session_manager import SessionManager
from dataecho.storage.storage_manager import StorageManager
from dataecho.websocket.connection_manager import ConnectionManager
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter(prefix="/api/v1", tags=["generation"])

# Global instances (will be injected via dependency injection in future)
session_manager = SessionManager()
storage_manager = StorageManager()
connection_manager = ConnectionManager()


@router.post("/sessions/{session_id}/generate")
async def start_data_generation(session_id: str, request: DataGenerationRequest):
    """Start the data generation workflow."""
    try:
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        if not session.get("csv_file_path"):
            raise HTTPException(status_code=400, detail="No CSV file uploaded for this session")
        
        # Initialize orchestrator
        orchestrator = WorkflowOrchestrator(
            session_id=session_id,
            connection_manager=connection_manager,
            storage_manager=storage_manager
        )
        
        # Start workflow asynchronously
        await orchestrator.start_workflow(
            csv_file_path=session["csv_file_path"],
            domain=session["domain"],
            user_context=session["user_context"],
            n_rows=request.n_rows,
            batch_size=request.batch_size
        )
        
        return {"message": "Data generation workflow started", "session_id": session_id}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting data generation: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to start data generation")


@router.get("/sessions/{session_id}/results")
async def get_results(session_id: str):
    """Get generated results for a session with enhanced data preview and quality metrics."""
    try:
        # Get session to check status
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        # Get all agent results
        results = await storage_manager.get_session_results(session_id)

        # Initialize response data
        response_data = {
            "session_id": session_id,
            "status": session.get("status", "unknown"),
            "results": results,
            "preview_data": [],
            "quality_metrics": {},
            "generation_summary": {}
        }

        # If generation is completed, add enhanced data
        if session.get("status") == "completed" and "generator" in results:
            generator_result = results["generator"]
            generated_data = generator_result.get("generated_data", [])

            # Add preview data (first 10 rows)
            response_data["preview_data"] = generated_data[:10] if generated_data else []

            # Calculate quality metrics
            total_rows = len(generated_data)
            response_data["quality_metrics"] = {
                "total_rows_generated": total_rows,
                "data_consistency": generator_result.get("quality_score", 0.95),
                "generation_time": generator_result.get("generation_time", "N/A"),
                "completion_rate": 100.0 if total_rows > 0 else 0.0
            }

            # Add generation summary
            response_data["generation_summary"] = {
                "profiler_insights": results.get("profiler", {}).get("insights", "N/A"),
                "dependency_relationships": len(results.get("dependency", {}).get("relationships", [])),
                "columns_generated": len(generated_data[0].keys()) if generated_data else 0,
                "data_types_detected": results.get("profiler", {}).get("column_types", {})
            }

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting results: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get results")


@router.get("/sessions/{session_id}/preview")
async def get_data_preview(session_id: str, limit: int = 10):
    """Get a preview of generated data with specified limit."""
    try:
        # Get session to check status
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.get("status") != "completed":
            raise HTTPException(status_code=400, detail="Data generation not completed yet")

        # Get generator results
        generator_result = await storage_manager.get_agent_result(session_id, "generator")
        if not generator_result:
            raise HTTPException(status_code=404, detail="Generated data not found")

        generated_data = generator_result.get("generated_data", [])
        preview_data = generated_data[:limit] if generated_data else []

        return {
            "session_id": session_id,
            "total_rows": len(generated_data),
            "preview_rows": len(preview_data),
            "columns": list(generated_data[0].keys()) if generated_data else [],
            "data": preview_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data preview: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get data preview")


@router.get("/sessions/{session_id}/quality")
async def get_quality_metrics(session_id: str):
    """Get detailed quality metrics for generated data."""
    try:
        # Get session to check status
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.get("status") != "completed":
            raise HTTPException(status_code=400, detail="Data generation not completed yet")

        # Get all agent results
        results = await storage_manager.get_session_results(session_id)

        # Extract quality metrics from different agents
        profiler_result = results.get("profiler", {})
        dependency_result = results.get("dependency", {})
        generator_result = results.get("generator", {})

        generated_data = generator_result.get("generated_data", [])

        quality_metrics = {
            "session_id": session_id,
            "overall_quality": generator_result.get("quality_score", 0.95),
            "data_consistency": generator_result.get("consistency_score", 0.90),
            "generation_time": generator_result.get("generation_time", "N/A"),
            "total_rows": len(generated_data),
            "columns_count": len(generated_data[0].keys()) if generated_data else 0,
            "profiler_insights": {
                "data_types": profiler_result.get("column_types", {}),
                "null_percentages": profiler_result.get("null_percentages", {}),
                "unique_counts": profiler_result.get("unique_counts", {}),
                "statistical_summary": profiler_result.get("statistical_summary", {})
            },
            "dependency_analysis": {
                "relationships_found": len(dependency_result.get("relationships", [])),
                "correlation_strength": dependency_result.get("correlation_strength", "N/A"),
                "dependency_score": dependency_result.get("dependency_score", 0.85)
            },
            "generation_performance": {
                "completion_rate": 100.0 if len(generated_data) > 0 else 0.0,
                "error_rate": generator_result.get("error_rate", 0.0),
                "processing_speed": generator_result.get("processing_speed", "N/A")
            }
        }

        return quality_metrics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting quality metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get quality metrics")


@router.get("/sessions/{session_id}/download")
async def download_generated_data(session_id: str):
    """Download generated synthetic data as CSV."""
    try:
        # Check if session exists and is completed
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")

        if session.get("status") != "completed":
            raise HTTPException(status_code=400, detail="Data generation not completed yet")

        # Check if final CSV already exists
        final_csv_path = os.path.join(storage_manager.results_dir, f"{session_id}_final_output.csv")

        if not os.path.exists(final_csv_path):
            # Convert generator JSON results to CSV
            generator_result_path = os.path.join(storage_manager.results_dir, f"{session_id}_generator.json")

            if not os.path.exists(generator_result_path):
                raise HTTPException(status_code=404, detail="Generated data not found")

            # Load generator results
            with open(generator_result_path, 'r', encoding='utf-8') as f:
                result = json.load(f)

            generated_data = result.get("generated_data", [])
            if not generated_data:
                raise HTTPException(status_code=404, detail="No generated data found")

            # Create CSV file
            fieldnames = list(generated_data[0].keys())
            with open(final_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(generated_data)

            logger.info(f"Created final CSV file: {final_csv_path}")

        # Return the CSV file
        return FileResponse(
            path=final_csv_path,
            media_type='text/csv',
            filename=f"{session_id}_generated_data.csv",
            headers={
                "Content-Disposition": f"attachment; filename={session_id}_generated_data.csv"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading generated data: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to download generated data")


@router.post("/run-complete-workflow")
async def run_complete_workflow(
    background_tasks: BackgroundTasks,
    domain: str = Form(...),
    user_context: str = Form(...),  # JSON stringified
    n_rows: int = Form(...),
    batch_size: int = Form(...),
    file: UploadFile = File(...)
):
    """Run complete workflow in a single request (for testing/simple usage)."""
    # Step 1: Create session
    session_id = await session_manager.create_session(
        domain=domain,
        user_context=user_context  # assumed to be raw JSON string
    )

    print(f"Session created: {session_id}")

    # Step 2: Upload CSV
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Only CSV files are allowed")

    file_path = await storage_manager.save_uploaded_file(session_id, file)
    await session_manager.update_session(session_id, {"csv_file_path": file_path})

    # Step 3: Trigger orchestrator
    orchestrator = WorkflowOrchestrator(
        session_id=session_id,
        connection_manager=connection_manager,
        storage_manager=storage_manager
    )

    await orchestrator.start_workflow(
        csv_file_path=file_path,
        domain=domain,
        user_context=user_context,  # must parse it for orchestrator
        n_rows=n_rows,
        batch_size=batch_size
    )

    # Step 4: Wait for completion
    while True:
        session = await session_manager.get_session(session_id)
        if session and session.get("status") == "completed":
            break
        await asyncio.sleep(5)

    # Step 5: Convert generated JSON to CSV
    generator_result_path = os.path.join(storage_manager.results_dir, f"{session_id}_generator.json")

    with open(generator_result_path, 'r', encoding='utf-8') as f:
        result = json.load(f)

    generated_data = result.get("generated_data", [])
    if not generated_data:
        raise HTTPException(status_code=500, detail="No generated data found")

    final_csv_path = os.path.join(storage_manager.results_dir, f"{session_id}_final_output.csv")

    fieldnames = list(generated_data[0].keys())
    with open(final_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(generated_data)

    # Step 6: Return CSV file and delete it in background
    background_tasks.add_task(os.unlink, final_csv_path)

    return FileResponse(
        path=final_csv_path,
        media_type='text/csv',
        filename=f"{session_id}_generated.csv",
        headers={
            "Content-Disposition": f"attachment; filename={session_id}_generated.csv"
        },
        background=background_tasks
    )