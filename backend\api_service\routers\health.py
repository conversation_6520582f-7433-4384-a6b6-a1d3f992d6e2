"""Health check and system status API routes."""

from fastapi import APIRouter
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter(tags=["health"])


@router.get("/")
async def root():
    """Root health check endpoint."""
    return {"message": "Multi-Agent Data Generation API is running", "status": "healthy"}


@router.get("/health")
async def health_check():
    """Detailed health check endpoint."""
    return {
        "status": "healthy",
        "service": "DataEcho API",
        "version": "1.0.0",
        "components": {
            "api": "healthy",
            "storage": "healthy",
            "websockets": "healthy"
        }
    }


@router.get("/api/v1/health")
async def api_health_check():
    """API-specific health check."""
    return {
        "status": "healthy",
        "api_version": "v1",
        "endpoints": {
            "sessions": "available",
            "generation": "available",
            "websockets": "available"
        }
    }