"""Session management API routes."""

from fastapi import APIRouter, HTTPException, UploadFile, File
from dataecho.models.requests import SessionRequest
from dataecho.models.responses import SessionResponse, StatusResponse
from dataecho.services.session_manager import SessionManager
from dataecho.storage.storage_manager import StorageManager
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter(prefix="/api/v1/sessions", tags=["sessions"])

# Global instances (will be injected via dependency injection in future)
session_manager = SessionManager()
storage_manager = StorageManager()


@router.post("", response_model=SessionResponse)
async def create_session(request: SessionRequest):
    """Create a new session."""
    try:
        session_id = await session_manager.create_session(
            domain=request.domain,
            user_context=request.user_context
        )
        return SessionResponse(
            session_id=session_id,
            status="created",
            message="Session created successfully"
        )
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.post("/{session_id}/upload-csv")
async def upload_csv(session_id: str, file: UploadFile = File(...)):
    """Upload CSV file for processing."""
    try:
        if not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="Only CSV files are allowed")
        
        # Save uploaded file
        file_path = await storage_manager.save_uploaded_file(session_id, file)
        
        # Update session with file info
        await session_manager.update_session(session_id, {"csv_file_path": file_path})
        
        return {"message": "CSV file uploaded successfully", "file_path": file_path}
    
    except Exception as e:
        logger.error(f"Error uploading CSV: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to upload CSV file")


@router.get("/{session_id}/status", response_model=StatusResponse)
async def get_session_status(session_id: str):
    """Get current session status."""
    try:
        session = await session_manager.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return StatusResponse(
            session_id=session_id,
            status=session.get("status", "unknown"),
            current_step=session.get("current_step", ""),
            progress=session.get("progress", 0),
            message=session.get("message", ""),
            data=session.get("data", {})
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get session status")


@router.delete("/{session_id}")
async def delete_session(session_id: str):
    """Delete a session and its associated data."""
    try:
        await session_manager.delete_session(session_id)
        await storage_manager.cleanup_session_data(session_id)
        return {"message": "Session deleted successfully"}
    
    except Exception as e:
        logger.error(f"Error deleting session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete session")