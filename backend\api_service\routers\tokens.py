"""Token usage tracking and analytics API routes."""

from fastapi import APIRouter, HTTPException
from dataecho.services.llm_service import LLMService
from dataecho.utils.logger import setup_logger
from typing import Dict, Any, List
import json
from pathlib import Path

logger = setup_logger(__name__)
router = APIRouter(prefix="/api/v1", tags=["tokens"])

# Global instance
llm_service = LLMService()


@router.get("/sessions/{session_id}/tokens")
async def get_session_token_usage(session_id: str) -> Dict[str, Any]:
    """Get token usage statistics for a specific session."""
    try:
        token_data = llm_service.get_session_tokens(session_id)
        
        # Format response for frontend
        response = {
            "session_id": session_id,
            "total_tokens": token_data.get("total_tokens", 0),
            "total_cost": token_data.get("total_cost", 0.0),
            "agents": {}
        }
        
        # Format agent-specific data
        for agent_name, agent_data in token_data.get("agents", {}).items():
            response["agents"][agent_name] = {
                "tokens_used": agent_data.get("tokens_used", 0),
                "cost": agent_data.get("cost", 0.0),
                "requests": agent_data.get("requests", 0),
                "last_call": agent_data.get("calls", [])[-1] if agent_data.get("calls") else None
            }
        
        return response
        
    except Exception as e:
        logger.error(f"Error retrieving token usage for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve token usage")


@router.get("/tokens/total")
async def get_total_token_usage() -> Dict[str, Any]:
    """Get total token usage across all sessions."""
    try:
        token_storage_dir = Path("token_usage")
        if not token_storage_dir.exists():
            return {
                "total_tokens": 0,
                "total_cost": 0.0,
                "session_count": 0,
                "sessions": []
            }
        
        total_tokens = 0
        total_cost = 0.0
        sessions = []
        
        # Aggregate data from all session files
        for token_file in token_storage_dir.glob("*_tokens.json"):
            try:
                with open(token_file, 'r') as f:
                    session_data = json.load(f)
                
                session_tokens = session_data.get("total_tokens", 0)
                session_cost = session_data.get("total_cost", 0.0)
                
                total_tokens += session_tokens
                total_cost += session_cost
                
                sessions.append({
                    "session_id": session_data.get("session_id"),
                    "tokens": session_tokens,
                    "cost": session_cost,
                    "created_at": session_data.get("created_at"),
                    "updated_at": session_data.get("updated_at")
                })
                
            except Exception as e:
                logger.warning(f"Error reading token file {token_file}: {str(e)}")
                continue
        
        return {
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "session_count": len(sessions),
            "sessions": sorted(sessions, key=lambda x: x.get("updated_at", ""), reverse=True)
        }
        
    except Exception as e:
        logger.error(f"Error retrieving total token usage: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve total token usage")


@router.get("/tokens/analytics")
async def get_token_analytics() -> Dict[str, Any]:
    """Get detailed token usage analytics and insights."""
    try:
        token_storage_dir = Path("token_usage")
        if not token_storage_dir.exists():
            return {
                "total_tokens": 0,
                "total_cost": 0.0,
                "agent_breakdown": {},
                "model_usage": {},
                "daily_usage": [],
                "cost_trends": []
            }
        
        total_tokens = 0
        total_cost = 0.0
        agent_breakdown = {}
        model_usage = {}
        daily_usage = {}
        
        # Process all session files
        for token_file in token_storage_dir.glob("*_tokens.json"):
            try:
                with open(token_file, 'r') as f:
                    session_data = json.load(f)
                
                total_tokens += session_data.get("total_tokens", 0)
                total_cost += session_data.get("total_cost", 0.0)
                
                # Agent breakdown
                for agent_name, agent_data in session_data.get("agents", {}).items():
                    if agent_name not in agent_breakdown:
                        agent_breakdown[agent_name] = {
                            "tokens": 0,
                            "cost": 0.0,
                            "requests": 0
                        }
                    
                    agent_breakdown[agent_name]["tokens"] += agent_data.get("tokens_used", 0)
                    agent_breakdown[agent_name]["cost"] += agent_data.get("cost", 0.0)
                    agent_breakdown[agent_name]["requests"] += agent_data.get("requests", 0)
                    
                    # Model usage tracking
                    for call in agent_data.get("calls", []):
                        model = call.get("model", "unknown")
                        if model not in model_usage:
                            model_usage[model] = {
                                "tokens": 0,
                                "cost": 0.0,
                                "requests": 0
                            }
                        
                        model_usage[model]["tokens"] += call.get("tokens", 0)
                        model_usage[model]["cost"] += call.get("cost", 0.0)
                        model_usage[model]["requests"] += 1
                        
                        # Daily usage (simplified - just by date)
                        date = call.get("timestamp", "")[:10]  # YYYY-MM-DD
                        if date:
                            if date not in daily_usage:
                                daily_usage[date] = {"tokens": 0, "cost": 0.0, "requests": 0}
                            daily_usage[date]["tokens"] += call.get("tokens", 0)
                            daily_usage[date]["cost"] += call.get("cost", 0.0)
                            daily_usage[date]["requests"] += 1
                
            except Exception as e:
                logger.warning(f"Error processing token file {token_file}: {str(e)}")
                continue
        
        # Convert daily usage to sorted list
        daily_usage_list = [
            {"date": date, **data} 
            for date, data in sorted(daily_usage.items())
        ]
        
        return {
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "agent_breakdown": agent_breakdown,
            "model_usage": model_usage,
            "daily_usage": daily_usage_list,
            "cost_trends": daily_usage_list  # Same data, different perspective
        }
        
    except Exception as e:
        logger.error(f"Error retrieving token analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve token analytics")


@router.delete("/sessions/{session_id}/tokens")
async def clear_session_tokens(session_id: str) -> Dict[str, str]:
    """Clear token usage data for a specific session."""
    try:
        token_file = Path("token_usage") / f"{session_id}_tokens.json"
        if token_file.exists():
            token_file.unlink()
            return {"message": f"Token usage data cleared for session {session_id}"}
        else:
            raise HTTPException(status_code=404, detail="Token usage data not found for this session")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing token data for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear token usage data")
