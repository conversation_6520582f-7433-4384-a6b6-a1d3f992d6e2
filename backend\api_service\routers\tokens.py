"""Token usage tracking and analytics API routes."""

from fastapi import APIRouter, HTTPException
from dataecho.services.llm_service import LLMService
from dataecho.database.service import db_service
from dataecho.utils.logger import setup_logger
from typing import Dict, Any, List
import json
from pathlib import Path

logger = setup_logger(__name__)
router = APIRouter(prefix="/api/v1", tags=["tokens"])

# Global instance
llm_service = LLMService()


@router.get("/sessions/{session_id}/tokens")
async def get_session_token_usage(session_id: str) -> Dict[str, Any]:
    """Get token usage statistics for a specific session."""
    try:
        # Use database service directly for better performance
        token_data = await db_service.get_session_token_usage(session_id)
        return token_data

    except Exception as e:
        logger.error(f"Error retrieving token usage for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve token usage")


@router.get("/tokens/total")
async def get_total_token_usage() -> Dict[str, Any]:
    """Get total token usage across all sessions."""
    try:
        return await db_service.get_total_token_usage()

    except Exception as e:
        logger.error(f"Error retrieving total token usage: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve total token usage")


@router.get("/tokens/analytics")
async def get_token_analytics() -> Dict[str, Any]:
    """Get detailed token usage analytics and insights."""
    try:
        return await db_service.get_token_analytics()

    except Exception as e:
        logger.error(f"Error retrieving token analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve token analytics")


@router.delete("/sessions/{session_id}/tokens")
async def clear_session_tokens(session_id: str) -> Dict[str, str]:
    """Clear token usage data for a specific session."""
    try:
        success = await db_service.clear_session_token_data(session_id)
        if success:
            return {"message": f"Token usage data cleared for session {session_id}"}
        else:
            raise HTTPException(status_code=404, detail="Token usage data not found for this session")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error clearing token data for session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear token usage data")
