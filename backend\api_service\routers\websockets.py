"""WebSocket API routes for real-time updates."""

from fastapi import APIRout<PERSON>, WebSocket, WebSocketDisconnect
from dataecho.websocket.connection_manager import ConnectionManager
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)
router = APIRouter(tags=["websockets"])

# Create connection manager instance
connection_manager = ConnectionManager()


@router.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time updates."""
    await connection_manager.connect(websocket, session_id)
    try:
        while True:
            # Keep connection alive and handle any incoming messages
            data = await websocket.receive_text()
            # Process any client messages if needed
            await websocket.send_text(f"Message received for session {session_id}: {data}")
    
    except WebSocketDisconnect:
        connection_manager.disconnect(session_id)
        logger.info(f"WebSocket disconnected for session: {session_id}")