#!/usr/bin/env python3

import asyncio
import aiohttp
import json

async def debug_frontend_issue():
    """Debug why the frontend is not showing results display"""
    
    print("🔍 Debugging Frontend Results Display Issue")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 1. Check if backend is responding
        try:
            async with session.get('http://localhost:8000/health') as response:
                if response.status == 200:
                    print("✅ Backend is responding")
                else:
                    print(f"❌ Backend health check failed: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Backend is not accessible: {e}")
            return
        
        # 2. Check if frontend is responding
        try:
            async with session.get('http://localhost:3000') as response:
                if response.status == 200:
                    print("✅ Frontend is responding")
                else:
                    print(f"❌ Frontend health check failed: {response.status}")
        except Exception as e:
            print(f"❌ Frontend is not accessible: {e}")
        
        # 3. Create a test session
        print("\n📝 Creating test session...")
        try:
            async with session.post('http://localhost:8000/api/v1/sessions', json={
                'domain': 'debug_test',
                'user_context': 'Debugging frontend results display'
            }) as response:
                if response.status == 200:
                    session_data = await response.json()
                    session_id = session_data['session_id']
                    print(f"✅ Session created: {session_id}")
                else:
                    print(f"❌ Failed to create session: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Session creation failed: {e}")
            return
        
        # 4. Upload test CSV
        print("\n📤 Uploading test CSV...")
        csv_content = '''Name,Age,Department,Salary
Alice Johnson,28,Engineering,75000
Bob Smith,32,Marketing,65000
Carol Davis,29,Engineering,78000
David Wilson,35,Sales,70000
Eva Brown,26,HR,60000'''
        
        try:
            data = aiohttp.FormData()
            data.add_field('file', csv_content, filename='employees.csv', content_type='text/csv')
            
            async with session.post(f'http://localhost:8000/api/v1/sessions/{session_id}/upload-csv', data=data) as response:
                if response.status == 200:
                    print("✅ CSV uploaded successfully")
                else:
                    print(f"❌ CSV upload failed: {response.status}")
                    return
        except Exception as e:
            print(f"❌ CSV upload failed: {e}")
            return
        
        # 5. Start generation
        print("\n🚀 Starting generation...")
        try:
            async with session.post(f'http://localhost:8000/api/v1/sessions/{session_id}/generate', json={
                'n_rows': 10,
                'batch_size': 10
            }) as response:
                if response.status == 200:
                    print("✅ Generation started")
                else:
                    print(f"❌ Generation failed to start: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Generation start failed: {e}")
            return
        
        # 6. Monitor progress
        print("\n⏳ Monitoring progress...")
        for i in range(120):  # Wait up to 2 minutes
            try:
                async with session.get(f'http://localhost:8000/api/v1/sessions/{session_id}/status') as response:
                    if response.status == 200:
                        status_data = await response.json()
                        status = status_data.get('status')
                        progress = status_data.get('progress', 0)
                        
                        print(f"Status: {status}, Progress: {progress}%")
                        
                        if status == 'completed':
                            print("✅ Generation completed!")
                            break
                        elif status == 'failed':
                            print("❌ Generation failed!")
                            print(f"Error: {status_data.get('error', 'Unknown error')}")
                            return
                    else:
                        print(f"❌ Status check failed: {response.status}")
                        
            except Exception as e:
                print(f"❌ Status check error: {e}")
            
            await asyncio.sleep(1)
        
        # 7. Check results endpoints
        print("\n📊 Checking results endpoints...")
        
        # Check results
        try:
            async with session.get(f'http://localhost:8000/api/v1/sessions/{session_id}/results') as response:
                if response.status == 200:
                    results = await response.json()
                    print(f"✅ Results endpoint working: {len(results.get('data', []))} rows")
                else:
                    print(f"❌ Results endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Results endpoint error: {e}")
        
        # Check preview
        try:
            async with session.get(f'http://localhost:8000/api/v1/sessions/{session_id}/preview?limit=5') as response:
                if response.status == 200:
                    preview = await response.json()
                    print(f"✅ Preview endpoint working: {len(preview.get('data', []))} rows")
                else:
                    print(f"❌ Preview endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Preview endpoint error: {e}")
        
        # Check quality metrics
        try:
            async with session.get(f'http://localhost:8000/api/v1/sessions/{session_id}/quality') as response:
                if response.status == 200:
                    quality = await response.json()
                    print(f"✅ Quality endpoint working: {len(quality.get('metrics', {}))} metrics")
                else:
                    print(f"❌ Quality endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Quality endpoint error: {e}")
        
        # Check token usage
        try:
            async with session.get(f'http://localhost:8000/api/v1/sessions/{session_id}/tokens') as response:
                if response.status == 200:
                    tokens = await response.json()
                    print(f"✅ Token endpoint working: {tokens.get('total_tokens', 0)} total tokens")
                else:
                    print(f"❌ Token endpoint failed: {response.status}")
        except Exception as e:
            print(f"❌ Token endpoint error: {e}")
        
        print(f"\n🎯 SUMMARY:")
        print(f"Session ID: {session_id}")
        print(f"Status: completed")
        print(f"Frontend should now show:")
        print(f"  - Results display instead of upload form")
        print(f"  - Data preview with download button")
        print(f"  - Quality metrics")
        print(f"  - Token usage in agent cards")
        print(f"  - New Session button")
        print(f"\n🌐 Check frontend at: http://localhost:3000")

if __name__ == "__main__":
    asyncio.run(debug_frontend_issue())
