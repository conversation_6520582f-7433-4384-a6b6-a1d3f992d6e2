services:
  api-service:
    build:
      context: .
      dockerfile: backend/api_service/Dockerfile
    container_name: dataecho-api
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./sessions:/app/sessions
      - ./results:/app/results
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DATABASE_URL=******************************************/dataecho
    env_file:
      - .env
    depends_on:
      - postgres
    restart: unless-stopped

  agent-service:
    build:
      context: .
      dockerfile: backend/agent_service/Dockerfile
    container_name: dataecho-agent
    volumes:
      - ./uploads:/app/uploads
      - ./sessions:/app/sessions
      - ./results:/app/results
    environment:
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - D<PERSON><PERSON><PERSON><PERSON>_URL=******************************************/dataecho
    env_file:
      - .env
    depends_on:
      - postgres
    restart: unless-stopped

  postgres:
    image: postgres:17-alpine
    container_name: dataecho-postgres
    environment:
      - POSTGRES_DB=dataecho
      - POSTGRES_USER=dataecho
      - POSTGRES_PASSWORD=secret
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data: {}

