#!/bin/bash

# --- CONFIGURATION ---
DOCKER_USERNAME="skm64060"
DOCKER_PAT="************************************"
IMAGE="dataecho"
TAG="v4"
IMAGE_NAME="$DOCKER_USERNAME/$IMAGE:$TAG"

# --- LO<PERSON>N TO DOCKER HUB ---
echo "🔐 Logging into DockerHub..."
echo "$DOCKER_PAT" | docker login --username "$DOCKER_USERNAME" --password-stdin

if [ $? -ne 0 ]; then
  echo "❌ Docker login failed. Check your PAT and username."
  exit 1
fi

# --- BUILD IMAGE ---
echo "📦 Building image: $IMAGE_NAME"
docker build -t "$IMAGE_NAME" .

# --- TAG LATEST ---
echo "🏷️ Tagging $IMAGE_NAME as latest..."
docker tag "$IMAGE_NAME" "$DOCKER_USERNAME/$IMAGE:latest"

# --- PUSH IMAGES ---
echo "📤 Pushing image: $IMAGE_NAME"
docker push "$IMAGE_NAME"

echo "📤 Pushing image: $DOCKER_USERNAME/$IMAGE:latest"
docker push "$DOCKER_USERNAME/$IMAGE:latest"

echo "✅ Docker image $IMAGE_NAME and latest pushed successfully."
