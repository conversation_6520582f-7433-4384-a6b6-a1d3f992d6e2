{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/features/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'motion/react';\nimport { Zap } from 'lucide-react';\nimport { Badge } from '@/components/ui/badge';\n\ninterface HeaderProps {\n  totalTokens?: number;\n  isAnimating?: boolean;\n}\n\nexport function Header({ totalTokens = 0, isAnimating = false }: HeaderProps) {\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-md\">\n      <div className=\"container mx-auto flex h-16 items-center justify-between px-6\">\n        {/* Logo and Title */}\n        <motion.div \n          className=\"flex items-center gap-3\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <div className=\"flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-lg\">\n            DE\n          </div>\n          <div>\n            <h1 className=\"text-xl font-bold text-foreground\">DataEcho</h1>\n            <p className=\"text-xs text-muted-foreground\">AI Synthetic Data Platform</p>\n          </div>\n        </motion.div>\n\n        {/* Token Counter */}\n        <motion.div\n          className=\"token-counter\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n        >\n          <motion.div\n            animate={isAnimating ? { scale: [1, 1.1, 1] } : {}}\n            transition={{ duration: 0.3 }}\n          >\n            <Zap className=\"h-4 w-4 text-green-500\" />\n          </motion.div>\n          \n          <motion.span\n            className=\"font-mono text-sm font-medium\"\n            key={totalTokens} // This will trigger re-animation when tokens change\n            initial={{ scale: 1.2, color: '#10b981' }}\n            animate={{ scale: 1, color: '#374151' }}\n            transition={{ duration: 0.3 }}\n          >\n            {totalTokens.toLocaleString()}\n          </motion.span>\n          \n          <Badge variant=\"secondary\" className=\"text-xs\">\n            tokens\n          </Badge>\n        </motion.div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,SAAS,OAAO,EAAE,cAAc,CAAC,EAAE,cAAc,KAAK,EAAe;IAC1E,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;sCAAmI;;;;;;sCAGlJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAKjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;;sCAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS,cAAc;gCAAE,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BAAC,IAAI,CAAC;4BACjD,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BAEV,SAAS;gCAAE,OAAO;gCAAK,OAAO;4BAAU;4BACxC,SAAS;gCAAE,OAAO;gCAAG,OAAO;4BAAU;4BACtC,YAAY;gCAAE,UAAU;4BAAI;sCAE3B,YAAY,cAAc;2BALtB;;;;;sCAQP,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/constants/index.ts"], "sourcesContent": ["// DataEcho Frontend Constants\n\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\nexport const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';\n\nexport const AGENTS = {\n  PROFILER: {\n    name: 'Profiler Agent',\n    description: 'Analyzes data patterns and distributions',\n    icon: '📊',\n    color: 'blue'\n  },\n  DEPENDENCY: {\n    name: 'Dependency Agent', \n    description: 'Maps relationships between columns',\n    icon: '🔗',\n    color: 'purple'\n  },\n  GENERATOR: {\n    name: 'Generator Agent',\n    description: 'Creates synthetic data based on patterns',\n    icon: '⚡',\n    color: 'green'\n  }\n} as const;\n\nexport const DOMAIN_SUGGESTIONS = [\n  'Financial Services',\n  'Healthcare',\n  'Retail & E-commerce',\n  'Manufacturing',\n  'Technology',\n  'Education',\n  'Real Estate',\n  'Transportation',\n  'Energy & Utilities',\n  'Government'\n] as const;\n\nexport const BATCH_SIZE_OPTIONS = [\n  { label: 'Small (100 rows/batch)', value: 100 },\n  { label: 'Medium (500 rows/batch)', value: 500 },\n  { label: 'Large (1000 rows/batch)', value: 1000 },\n  { label: 'Extra Large (2000 rows/batch)', value: 2000 }\n] as const;\n\nexport const FILE_CONSTRAINTS = {\n  MAX_SIZE: 10 * 1024 * 1024, // 10MB\n  ALLOWED_TYPES: ['.csv'],\n  MIME_TYPES: ['text/csv', 'application/csv']\n} as const;\n\nexport const ANIMATION_DURATIONS = {\n  FAST: 0.2,\n  NORMAL: 0.3,\n  SLOW: 0.5\n} as const;\n\nexport const WEBSOCKET_EVENTS = {\n  STATUS_UPDATE: 'status_update',\n  PROGRESS: 'progress',\n  COMPLETION: 'completion',\n  ERROR: 'error'\n} as const;\n\nexport const SESSION_STATUS = {\n  CREATED: 'created',\n  RUNNING: 'running', \n  COMPLETED: 'completed',\n  FAILED: 'failed'\n} as const;\n\nexport const AGENT_STATUS = {\n  IDLE: 'idle',\n  RUNNING: 'running',\n  COMPLETED: 'completed',\n  FAILED: 'failed'\n} as const;\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;;;;;;;;;;AAEvB,MAAM,eAAe,6DAAmC;AACxD,MAAM,cAAc,2DAAkC;AAEtD,MAAM,SAAS;IACpB,UAAU;QACR,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;IACT;AACF;AAEO,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,qBAAqB;IAChC;QAAE,OAAO;QAA0B,OAAO;IAAI;IAC9C;QAAE,OAAO;QAA2B,OAAO;IAAI;IAC/C;QAAE,OAAO;QAA2B,OAAO;IAAK;IAChD;QAAE,OAAO;QAAiC,OAAO;IAAK;CACvD;AAEM,MAAM,mBAAmB;IAC9B,UAAU,KAAK,OAAO;IACtB,eAAe;QAAC;KAAO;IACvB,YAAY;QAAC;QAAY;KAAkB;AAC7C;AAEO,MAAM,sBAAsB;IACjC,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAEO,MAAM,mBAAmB;IAC9B,eAAe;IACf,UAAU;IACV,YAAY;IACZ,OAAO;AACT;AAEO,MAAM,iBAAiB;IAC5B,SAAS;IACT,SAAS;IACT,WAAW;IACX,QAAQ;AACV;AAEO,MAAM,eAAe;IAC1B,MAAM;IACN,SAAS;IACT,WAAW;IACX,QAAQ;AACV", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/features/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback, useRef } from 'react';\nimport { motion, AnimatePresence } from 'motion/react';\nimport { Upload, File, CheckCircle, X, AlertCircle } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { FILE_CONSTRAINTS } from '@/constants';\nimport type { FileUpload } from '@/types';\n\ninterface FileUploadProps {\n  onFileSelect: (file: File) => void;\n  onFileRemove: () => void;\n  uploadedFile?: FileUpload | null;\n  isUploading?: boolean;\n  error?: string | null;\n}\n\nexport function FileUpload({ \n  onFileSelect, \n  onFileRemove, \n  uploadedFile, \n  isUploading = false,\n  error \n}: FileUploadProps) {\n  const [isDragOver, setIsDragOver] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const validateFile = useCallback((file: File): string | null => {\n    if (file.size > FILE_CONSTRAINTS.MAX_SIZE) {\n      return `File size must be less than ${FILE_CONSTRAINTS.MAX_SIZE / (1024 * 1024)}MB`;\n    }\n    \n    if (!FILE_CONSTRAINTS.ALLOWED_TYPES.some(type => file.name.toLowerCase().endsWith(type))) {\n      return 'Only CSV files are allowed';\n    }\n    \n    return null;\n  }, []);\n\n  const handleFileSelect = useCallback((file: File) => {\n    const validationError = validateFile(file);\n    if (validationError) {\n      // Handle validation error - could show toast or set error state\n      console.error('File validation failed:', validationError);\n      return;\n    }\n    \n    onFileSelect(file);\n  }, [validateFile, onFileSelect]);\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  }, []);\n\n  const handleDrop = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n    \n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  }, [handleFileSelect]);\n\n  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(e.target.files || []);\n    if (files.length > 0) {\n      handleFileSelect(files[0]);\n    }\n  }, [handleFileSelect]);\n\n  const handleBrowseClick = useCallback(() => {\n    fileInputRef.current?.click();\n  }, []);\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"space-y-2\">\n        <h3 className=\"text-lg font-semibold\">Upload Your CSV Data</h3>\n        <p className=\"text-sm text-muted-foreground\">\n          Upload your CSV file and let our AI agents create realistic synthetic data\n        </p>\n      </div>\n\n      <AnimatePresence mode=\"wait\">\n        {!uploadedFile ? (\n          <motion.div\n            key=\"upload-zone\"\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.95 }}\n            transition={{ duration: 0.2 }}\n            className={`upload-zone ${isDragOver ? 'dragover' : ''}`}\n            onDragOver={handleDragOver}\n            onDragLeave={handleDragLeave}\n            onDrop={handleDrop}\n          >\n            <motion.div\n              className=\"flex flex-col items-center gap-4\"\n              animate={isDragOver ? { scale: 1.05 } : { scale: 1 }}\n              transition={{ duration: 0.2 }}\n            >\n              <motion.div\n                className=\"flex h-16 w-16 items-center justify-center rounded-full bg-primary/10\"\n                animate={isDragOver ? { rotate: 5 } : { rotate: 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Upload className=\"h-8 w-8 text-primary\" />\n              </motion.div>\n              \n              <div className=\"text-center\">\n                <p className=\"text-lg font-medium\">\n                  {isDragOver ? 'Drop your CSV file here' : 'Drop your CSV file here'}\n                </p>\n                <p className=\"text-sm text-muted-foreground\">\n                  or click to browse and select your data file\n                </p>\n              </div>\n              \n              <Button onClick={handleBrowseClick} variant=\"outline\">\n                Select CSV File\n              </Button>\n              \n              <p className=\"text-xs text-muted-foreground\">\n                Supports CSV files up to {FILE_CONSTRAINTS.MAX_SIZE / (1024 * 1024)}MB\n              </p>\n            </motion.div>\n\n            <input\n              ref={fileInputRef}\n              type=\"file\"\n              accept=\".csv\"\n              onChange={handleFileInputChange}\n              className=\"hidden\"\n            />\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"uploaded-file\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"rounded-xl border bg-card p-4\"\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20\">\n                  {uploadedFile.status === 'completed' ? (\n                    <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                  ) : uploadedFile.status === 'error' ? (\n                    <AlertCircle className=\"h-5 w-5 text-red-600\" />\n                  ) : (\n                    <File className=\"h-5 w-5 text-blue-600\" />\n                  )}\n                </div>\n                \n                <div>\n                  <p className=\"font-medium\">{uploadedFile.name}</p>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {formatFileSize(uploadedFile.size)} • Ready for processing\n                  </p>\n                </div>\n              </div>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={onFileRemove}\n                className=\"h-8 w-8 p-0\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n\n            {isUploading && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                className=\"mt-3 space-y-2\"\n              >\n                <Progress value={uploadedFile.progress} className=\"h-2\" />\n                <p className=\"text-xs text-muted-foreground\">\n                  Uploading... {uploadedFile.progress}%\n                </p>\n              </motion.div>\n            )}\n\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                className=\"mt-3 rounded-md bg-red-50 p-3 dark:bg-red-900/20\"\n              >\n                <p className=\"text-sm text-red-600 dark:text-red-400\">{error}</p>\n              </motion.div>\n            )}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAkBO,SAAS,WAAW,EACzB,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,cAAc,KAAK,EACnB,KAAK,EACW;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,IAAI,KAAK,IAAI,GAAG,yHAAA,CAAA,mBAAgB,CAAC,QAAQ,EAAE;YACzC,OAAO,CAAC,4BAA4B,EAAE,yHAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;QACrF;QAEA,IAAI,CAAC,yHAAA,CAAA,mBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ;YACxF,OAAO;QACT;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,MAAM,kBAAkB,aAAa;QACrC,IAAI,iBAAiB;YACnB,gEAAgE;YAChE,QAAQ,KAAK,CAAC,2BAA2B;YACzC;QACF;QAEA,aAAa;IACf,GAAG;QAAC;QAAc;KAAa;IAE/B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,EAAE,cAAc;QAChB,cAAc;IAChB,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,cAAc;QAEd,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB,KAAK,CAAC,EAAE;QAC3B;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,aAAa,OAAO,EAAE;IACxB,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;0BAK/C,8OAAC,yLAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBACnC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAW,CAAC,YAAY,EAAE,aAAa,aAAa,IAAI;oBACxD,YAAY;oBACZ,aAAa;oBACb,QAAQ;;sCAER,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS,aAAa;gCAAE,OAAO;4BAAK,IAAI;gCAAE,OAAO;4BAAE;4BACnD,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS,aAAa;wCAAE,QAAQ;oCAAE,IAAI;wCAAE,QAAQ;oCAAE;oCAClD,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,aAAa,4BAA4B;;;;;;sDAE5C,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAK/C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAmB,SAAQ;8CAAU;;;;;;8CAItD,8OAAC;oCAAE,WAAU;;wCAAgC;wCACjB,yHAAA,CAAA,mBAAgB,CAAC,QAAQ,GAAG,CAAC,OAAO,IAAI;wCAAE;;;;;;;;;;;;;sCAIxE,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU;4BACV,WAAU;;;;;;;mBA9CR;;;;yCAkDN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,aAAa,MAAM,KAAK,4BACvB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;uDACrB,aAAa,MAAM,KAAK,wBAC1B,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIpB,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe,aAAa,IAAI;;;;;;8DAC7C,8OAAC;oDAAE,WAAU;;wDACV,eAAe,aAAa,IAAI;wDAAE;;;;;;;;;;;;;;;;;;;8CAKzC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIhB,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,WAAU;;8CAEV,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,OAAO,aAAa,QAAQ;oCAAE,WAAU;;;;;;8CAClD,8OAAC;oCAAE,WAAU;;wCAAgC;wCAC7B,aAAa,QAAQ;wCAAC;;;;;;;;;;;;;wBAKzC,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;mBAxDvD;;;;;;;;;;;;;;;;AAgEhB", "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/features/AgentStatusCard.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'motion/react';\nimport { Badge } from '@/components/ui/badge';\nimport { Progress } from '@/components/ui/progress';\nimport type { AgentStatus } from '@/types';\nimport { AGENT_STATUS } from '@/constants';\n\ninterface AgentStatusCardProps {\n  agent: AgentStatus;\n  index: number;\n}\n\nexport function AgentStatusCard({ agent, index }: AgentStatusCardProps) {\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case AGENT_STATUS.IDLE:\n        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';\n      case AGENT_STATUS.RUNNING:\n        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300';\n      case AGENT_STATUS.COMPLETED:\n        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300';\n      case AGENT_STATUS.FAILED:\n        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300';\n      default:\n        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';\n    }\n  };\n\n  const getCardBorderColor = (status: string) => {\n    switch (status) {\n      case AGENT_STATUS.RUNNING:\n        return 'border-blue-200 dark:border-blue-800';\n      case AGENT_STATUS.COMPLETED:\n        return 'border-green-200 dark:border-green-800';\n      case AGENT_STATUS.FAILED:\n        return 'border-red-200 dark:border-red-800';\n      default:\n        return 'border-border';\n    }\n  };\n\n  const isActive = agent.status === AGENT_STATUS.RUNNING;\n  const isCompleted = agent.status === AGENT_STATUS.COMPLETED;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.4, delay: index * 0.1 }}\n      className={`agent-card ${getCardBorderColor(agent.status)}`}\n    >\n      {/* Animated background for active agents */}\n      {isActive && (\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl\"\n          animate={{ opacity: [0.3, 0.6, 0.3] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        />\n      )}\n\n      <div className=\"relative z-10\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center gap-3\">\n            <motion.div\n              className=\"text-2xl\"\n              animate={isActive ? { \n                rotate: [0, 5, -5, 0],\n                scale: [1, 1.1, 1]\n              } : {}}\n              transition={{ \n                duration: 2, \n                repeat: isActive ? Infinity : 0,\n                repeatType: \"reverse\"\n              }}\n            >\n              {agent.icon}\n            </motion.div>\n            <div>\n              <h3 className=\"font-semibold text-foreground\">{agent.name}</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                {agent.name === 'Profiler Agent' && 'Analyzes data patterns and distributions'}\n                {agent.name === 'Dependency Agent' && 'Maps relationships between columns'}\n                {agent.name === 'Generator Agent' && 'Creates synthetic data based on patterns'}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            {/* Activity Indicator */}\n            {isActive && (\n              <motion.div\n                className=\"w-2 h-2 bg-blue-500 rounded-full\"\n                animate={{ scale: [1, 1.2, 1], opacity: [1, 0.7, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              />\n            )}\n\n            <Badge className={getStatusColor(agent.status)} variant=\"secondary\">\n              {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}\n            </Badge>\n          </div>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"space-y-2 mb-4\">\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-muted-foreground\">Progress</span>\n            <span className=\"font-medium\">{agent.progress}%</span>\n          </div>\n          \n          <div className=\"progress-bar\">\n            <motion.div\n              className=\"progress-fill\"\n              initial={{ width: 0 }}\n              animate={{ width: `${agent.progress}%` }}\n              transition={{ duration: 0.5, ease: \"easeOut\" }}\n            />\n          </div>\n        </div>\n\n        {/* Status Message */}\n        <div className=\"space-y-3\">\n          <motion.p\n            key={agent.message} // Re-animate when message changes\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"text-sm text-muted-foreground\"\n          >\n            {agent.message}\n          </motion.p>\n\n          {/* Token Usage & Performance Metrics */}\n          <div className=\"space-y-2\">\n            {agent.tokens_used > 0 && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3 }}\n                className=\"flex items-center justify-between text-xs\"\n              >\n                <span className=\"text-muted-foreground\">Tokens Used</span>\n                <motion.span\n                  key={agent.tokens_used}\n                  initial={{ scale: 1.2, color: '#10b981' }}\n                  animate={{ scale: 1, color: '#6b7280' }}\n                  transition={{ duration: 0.3 }}\n                  className=\"font-mono font-medium\"\n                >\n                  {agent.tokens_used.toLocaleString()}\n                </motion.span>\n              </motion.div>\n            )}\n\n            {/* Estimated Cost */}\n            {agent.tokens_used > 0 && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: 0.1 }}\n                className=\"flex items-center justify-between text-xs\"\n              >\n                <span className=\"text-muted-foreground\">Est. Cost</span>\n                <span className=\"font-mono font-medium text-green-600\">\n                  ${((agent.tokens_used / 1000) * 0.002).toFixed(4)}\n                </span>\n              </motion.div>\n            )}\n\n            {/* Processing Time (if completed) */}\n            {isCompleted && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: 0.2 }}\n                className=\"flex items-center justify-between text-xs\"\n              >\n                <span className=\"text-muted-foreground\">Processing Time</span>\n                <span className=\"font-mono font-medium text-blue-600\">\n                  ~{Math.floor(Math.random() * 10) + 2}s\n                </span>\n              </motion.div>\n            )}\n          </div>\n        </div>\n\n        {/* Completion Checkmark Animation */}\n        {isCompleted && (\n          <motion.div\n            initial={{ scale: 0, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ \n              duration: 0.5, \n              delay: 0.2,\n              type: \"spring\",\n              stiffness: 200 \n            }}\n            className=\"absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 flex items-center justify-center\"\n          >\n            <motion.svg\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 0.3, delay: 0.5 }}\n              className=\"h-3 w-3 text-white\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              strokeWidth={3}\n            >\n              <motion.path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"M5 13l4 4L19 7\"\n              />\n            </motion.svg>\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AANA;;;;;AAaO,SAAS,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAwB;IACpE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK,yHAAA,CAAA,eAAY,CAAC,IAAI;gBACpB,OAAO;YACT,KAAK,yHAAA,CAAA,eAAY,CAAC,OAAO;gBACvB,OAAO;YACT,KAAK,yHAAA,CAAA,eAAY,CAAC,SAAS;gBACzB,OAAO;YACT,KAAK,yHAAA,CAAA,eAAY,CAAC,MAAM;gBACtB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK,yHAAA,CAAA,eAAY,CAAC,OAAO;gBACvB,OAAO;YACT,KAAK,yHAAA,CAAA,eAAY,CAAC,SAAS;gBACzB,OAAO;YACT,KAAK,yHAAA,CAAA,eAAY,CAAC,MAAM;gBACtB,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW,MAAM,MAAM,KAAK,yHAAA,CAAA,eAAY,CAAC,OAAO;IACtD,MAAM,cAAc,MAAM,MAAM,KAAK,yHAAA,CAAA,eAAY,CAAC,SAAS;IAE3D,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,WAAW,CAAC,WAAW,EAAE,mBAAmB,MAAM,MAAM,GAAG;;YAG1D,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAAC;gBACpC,YAAY;oBAAE,UAAU;oBAAG,QAAQ;gBAAS;;;;;;0BAIhD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS,WAAW;4CAClB,QAAQ;gDAAC;gDAAG;gDAAG,CAAC;gDAAG;6CAAE;4CACrB,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;wCACpB,IAAI,CAAC;wCACL,YAAY;4CACV,UAAU;4CACV,QAAQ,WAAW,WAAW;4CAC9B,YAAY;wCACd;kDAEC,MAAM,IAAI;;;;;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC,MAAM,IAAI;;;;;;0DACzD,8OAAC;gDAAE,WAAU;;oDACV,MAAM,IAAI,KAAK,oBAAoB;oDACnC,MAAM,IAAI,KAAK,sBAAsB;oDACrC,MAAM,IAAI,KAAK,qBAAqB;;;;;;;;;;;;;;;;;;;0CAK3C,8OAAC;gCAAI,WAAU;;oCAEZ,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAAE,SAAS;gDAAC;gDAAG;gDAAK;6CAAE;wCAAC;wCACpD,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;;;;;;kDAIhD,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAW,eAAe,MAAM,MAAM;wCAAG,SAAQ;kDACrD,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;kCAMjE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;;4CAAe,MAAM,QAAQ;4CAAC;;;;;;;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO,GAAG,MAAM,QAAQ,CAAC,CAAC,CAAC;oCAAC;oCACvC,YAAY;wCAAE,UAAU;wCAAK,MAAM;oCAAU;;;;;;;;;;;;;;;;;kCAMnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCAEP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAET,MAAM,OAAO;+BANT,MAAM,OAAO;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;oCACZ,MAAM,WAAW,GAAG,mBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDAEV,SAAS;oDAAE,OAAO;oDAAK,OAAO;gDAAU;gDACxC,SAAS;oDAAE,OAAO;oDAAG,OAAO;gDAAU;gDACtC,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;0DAET,MAAM,WAAW,CAAC,cAAc;+CAN5B,MAAM,WAAW;;;;;;;;;;;oCAY3B,MAAM,WAAW,GAAG,mBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;;oDAAuC;oDACnD,CAAC,AAAC,MAAM,WAAW,GAAG,OAAQ,KAAK,EAAE,OAAO,CAAC;;;;;;;;;;;;;oCAMpD,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;gDAAK,WAAU;;oDAAsC;oDAClD,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ9C,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BACV,UAAU;4BACV,OAAO;4BACP,MAAM;4BACN,WAAW;wBACb;wBACA,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,YAAY;4BAAE;4BACzB,SAAS;gCAAE,YAAY;4BAAE;4BACzB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;4BACV,MAAK;4BACL,SAAQ;4BACR,QAAO;4BACP,aAAa;sCAEb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,eAAc;gCACd,gBAAe;gCACf,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1404, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/features/ConfigurationForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'motion/react';\nimport { Setting<PERSON>, Play, Loader2 } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { DOMAIN_SUGGESTIONS, BATCH_SIZE_OPTIONS } from '@/constants';\nimport type { GenerationConfig } from '@/types';\n\ninterface ConfigurationFormProps {\n  onSubmit: (config: GenerationConfig) => void;\n  isLoading?: boolean;\n  disabled?: boolean;\n}\n\nexport function ConfigurationForm({ onSubmit, isLoading = false, disabled = false }: ConfigurationFormProps) {\n  const [config, setConfig] = useState<GenerationConfig>({\n    domain: '',\n    user_context: '',\n    n_rows: 1000,\n    batch_size: 500\n  });\n\n  const [errors, setErrors] = useState<Partial<GenerationConfig>>({});\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<GenerationConfig> = {};\n\n    if (!config.domain.trim()) {\n      newErrors.domain = 'Domain is required';\n    }\n\n    if (!config.user_context.trim()) {\n      newErrors.user_context = 'Context description is required';\n    }\n\n    if (config.n_rows < 10 || config.n_rows > 100000) {\n      newErrors.n_rows = 10; // Using number to indicate min value\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (validateForm()) {\n      onSubmit(config);\n    }\n  };\n\n  const handleDomainSelect = (domain: string) => {\n    setConfig(prev => ({ ...prev, domain }));\n    if (errors.domain) {\n      setErrors(prev => ({ ...prev, domain: undefined }));\n    }\n  };\n\n  const handleBatchSizeSelect = (batchSize: string) => {\n    setConfig(prev => ({ ...prev, batch_size: parseInt(batchSize) }));\n  };\n\n  const isFormValid = config.domain && config.user_context && config.n_rows >= 10;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.4 }}\n    >\n      <Card className=\"card-gradient\">\n        <CardHeader>\n          <div className=\"flex items-center gap-2\">\n            <Settings className=\"h-5 w-5 text-primary\" />\n            <CardTitle>Generation Configuration</CardTitle>\n          </div>\n          <CardDescription>\n            Configure the parameters for synthetic data generation\n          </CardDescription>\n        </CardHeader>\n\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Domain Selection */}\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">\n                Domain <span className=\"text-red-500\">*</span>\n              </label>\n              <Select \n                value={config.domain} \n                onValueChange={handleDomainSelect}\n                disabled={disabled}\n              >\n                <SelectTrigger className={errors.domain ? 'border-red-500' : ''}>\n                  <SelectValue placeholder=\"Select your data domain\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {DOMAIN_SUGGESTIONS.map((domain) => (\n                    <SelectItem key={domain} value={domain}>\n                      {domain}\n                    </SelectItem>\n                  ))}\n                  <SelectItem value=\"custom\">Custom Domain</SelectItem>\n                </SelectContent>\n              </Select>\n              {errors.domain && (\n                <motion.p\n                  initial={{ opacity: 0, y: -10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"text-sm text-red-500\"\n                >\n                  {errors.domain}\n                </motion.p>\n              )}\n            </div>\n\n            {/* Custom Domain Input */}\n            {config.domain === 'custom' && (\n              <motion.div\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                className=\"space-y-2\"\n              >\n                <label className=\"text-sm font-medium\">Custom Domain</label>\n                <Input\n                  placeholder=\"Enter your custom domain\"\n                  value={config.domain === 'custom' ? '' : config.domain}\n                  onChange={(e) => setConfig(prev => ({ ...prev, domain: e.target.value }))}\n                  disabled={disabled}\n                />\n              </motion.div>\n            )}\n\n            {/* User Context */}\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">\n                Context Description <span className=\"text-red-500\">*</span>\n              </label>\n              <Textarea\n                placeholder=\"Describe your data context, use case, and any specific requirements...\"\n                value={config.user_context}\n                onChange={(e) => {\n                  setConfig(prev => ({ ...prev, user_context: e.target.value }));\n                  if (errors.user_context) {\n                    setErrors(prev => ({ ...prev, user_context: undefined }));\n                  }\n                }}\n                className={`min-h-[100px] ${errors.user_context ? 'border-red-500' : ''}`}\n                disabled={disabled}\n              />\n              {errors.user_context && (\n                <motion.p\n                  initial={{ opacity: 0, y: -10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"text-sm text-red-500\"\n                >\n                  {errors.user_context}\n                </motion.p>\n              )}\n              <p className=\"text-xs text-muted-foreground\">\n                Provide context about your data to help our AI agents generate more accurate synthetic data\n              </p>\n            </div>\n\n            {/* Generation Parameters */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Number of Rows</label>\n                <Input\n                  type=\"number\"\n                  min=\"10\"\n                  max=\"100000\"\n                  value={config.n_rows}\n                  onChange={(e) => {\n                    const value = parseInt(e.target.value) || 0;\n                    setConfig(prev => ({ ...prev, n_rows: value }));\n                    if (errors.n_rows) {\n                      setErrors(prev => ({ ...prev, n_rows: undefined }));\n                    }\n                  }}\n                  className={errors.n_rows ? 'border-red-500' : ''}\n                  disabled={disabled}\n                />\n                {errors.n_rows && (\n                  <motion.p\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"text-sm text-red-500\"\n                  >\n                    Must be between 10 and 100,000 rows\n                  </motion.p>\n                )}\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Batch Size</label>\n                <Select \n                  value={config.batch_size.toString()} \n                  onValueChange={handleBatchSizeSelect}\n                  disabled={disabled}\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {BATCH_SIZE_OPTIONS.map((option) => (\n                      <SelectItem key={option.value} value={option.value.toString()}>\n                        {option.label}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <p className=\"text-xs text-muted-foreground\">\n                  Larger batches are faster but use more memory\n                </p>\n              </div>\n            </div>\n\n            {/* Submit Button */}\n            <motion.div\n              className=\"pt-4\"\n              whileHover={{ scale: isFormValid && !disabled ? 1.02 : 1 }}\n              whileTap={{ scale: isFormValid && !disabled ? 0.98 : 1 }}\n            >\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={!isFormValid || disabled || isLoading}\n                size=\"lg\"\n              >\n                {isLoading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Starting Generation...\n                  </>\n                ) : (\n                  <>\n                    <Play className=\"mr-2 h-4 w-4\" />\n                    Start Data Generation\n                  </>\n                )}\n              </Button>\n            </motion.div>\n          </form>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAmBO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAE,WAAW,KAAK,EAA0B;IACzG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACrD,QAAQ;QACR,cAAc;QACd,QAAQ;QACR,YAAY;IACd;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,CAAC;IAEjE,MAAM,eAAe;QACnB,MAAM,YAAuC,CAAC;QAE9C,IAAI,CAAC,OAAO,MAAM,CAAC,IAAI,IAAI;YACzB,UAAU,MAAM,GAAG;QACrB;QAEA,IAAI,CAAC,OAAO,YAAY,CAAC,IAAI,IAAI;YAC/B,UAAU,YAAY,GAAG;QAC3B;QAEA,IAAI,OAAO,MAAM,GAAG,MAAM,OAAO,MAAM,GAAG,QAAQ;YAChD,UAAU,MAAM,GAAG,IAAI,qCAAqC;QAC9D;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,gBAAgB;YAClB,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE;YAAO,CAAC;QACtC,IAAI,OAAO,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAU,CAAC;QACnD;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,YAAY,SAAS;YAAW,CAAC;IACjE;IAEA,MAAM,cAAc,OAAO,MAAM,IAAI,OAAO,YAAY,IAAI,OAAO,MAAM,IAAI;IAE7E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;;sCAEb,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAKnB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;4CAAsB;0DAC9B,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAExC,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,OAAO,MAAM;wCACpB,eAAe;wCACf,UAAU;;0DAEV,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAW,OAAO,MAAM,GAAG,mBAAmB;0DAC3D,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;;oDACX,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,uBACvB,8OAAC,kIAAA,CAAA,aAAU;4DAAc,OAAO;sEAC7B;2DADc;;;;;kEAInB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;;;;;;;;;;;;;oCAG9B,OAAO,MAAM,kBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;kDAET,OAAO,MAAM;;;;;;;;;;;;4BAMnB,OAAO,MAAM,KAAK,0BACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,WAAU;;kDAEV,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO,OAAO,MAAM,KAAK,WAAW,KAAK,OAAO,MAAM;wCACtD,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACvE,UAAU;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;4CAAsB;0DACjB,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAErD,8OAAC,oIAAA,CAAA,WAAQ;wCACP,aAAY;wCACZ,OAAO,OAAO,YAAY;wCAC1B,UAAU,CAAC;4CACT,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;4CAC5D,IAAI,OAAO,YAAY,EAAE;gDACvB,UAAU,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,cAAc;oDAAU,CAAC;4CACzD;wCACF;wCACA,WAAW,CAAC,cAAc,EAAE,OAAO,YAAY,GAAG,mBAAmB,IAAI;wCACzE,UAAU;;;;;;oCAEX,OAAO,YAAY,kBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wCACP,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;kDAET,OAAO,YAAY;;;;;;kDAGxB,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAM/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,OAAO,MAAM;gDACpB,UAAU,CAAC;oDACT,MAAM,QAAQ,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oDAC1C,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ;wDAAM,CAAC;oDAC7C,IAAI,OAAO,MAAM,EAAE;wDACjB,UAAU,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,QAAQ;4DAAU,CAAC;oDACnD;gDACF;gDACA,WAAW,OAAO,MAAM,GAAG,mBAAmB;gDAC9C,UAAU;;;;;;4CAEX,OAAO,MAAM,kBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,WAAU;0DACX;;;;;;;;;;;;kDAML,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAsB;;;;;;0DACvC,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,OAAO,UAAU,CAAC,QAAQ;gDACjC,eAAe;gDACf,UAAU;;kEAEV,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;kEACX,yHAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,uBACvB,8OAAC,kIAAA,CAAA,aAAU;gEAAoB,OAAO,OAAO,KAAK,CAAC,QAAQ;0EACxD,OAAO,KAAK;+DADE,OAAO,KAAK;;;;;;;;;;;;;;;;0DAMnC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAOjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO,eAAe,CAAC,WAAW,OAAO;gCAAE;gCACzD,UAAU;oCAAE,OAAO,eAAe,CAAC,WAAW,OAAO;gCAAE;0CAEvD,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU,CAAC,eAAe,YAAY;oCACtC,MAAK;8CAEJ,0BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrD", "debugId": null}}, {"offset": {"line": 2267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/services/api.ts"], "sourcesContent": ["// DataEcho API Service\n\nimport { API_BASE_URL } from '@/constants';\nimport type { \n  Session, \n  TokenUsage, \n  GenerationConfig, \n  GenerationResult,\n  ApiResponse,\n  SessionResponse,\n  TokenUsageResponse,\n  GenerationResponse\n} from '@/types';\n\nclass ApiService {\n  private baseUrl: string;\n\n  constructor() {\n    this.baseUrl = API_BASE_URL;\n  }\n\n  private async request<T>(\n    endpoint: string, \n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const response = await fetch(`${this.baseUrl}${endpoint}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return { success: true, data };\n    } catch (error) {\n      console.error('API request failed:', error);\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Unknown error' \n      };\n    }\n  }\n\n  // Session Management\n  async createSession(domain: string, userContext: string): Promise<SessionResponse> {\n    return this.request<Session>('/api/v1/sessions', {\n      method: 'POST',\n      body: JSON.stringify({ domain, user_context: userContext }),\n    });\n  }\n\n  async getSession(sessionId: string): Promise<SessionResponse> {\n    return this.request<Session>(`/api/v1/sessions/${sessionId}/status`);\n  }\n\n  async deleteSession(sessionId: string): Promise<ApiResponse> {\n    return this.request(`/api/v1/sessions/${sessionId}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // File Upload\n  async uploadFile(sessionId: string, file: File): Promise<ApiResponse> {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    try {\n      const response = await fetch(`${this.baseUrl}/api/v1/sessions/${sessionId}/upload-csv`, {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return { success: true, data };\n    } catch (error) {\n      return { \n        success: false, \n        error: error instanceof Error ? error.message : 'Upload failed' \n      };\n    }\n  }\n\n  // Data Generation\n  async startGeneration(\n    sessionId: string, \n    config: GenerationConfig\n  ): Promise<GenerationResponse> {\n    return this.request<GenerationResult>(`/api/v1/sessions/${sessionId}/generate`, {\n      method: 'POST',\n      body: JSON.stringify({\n        n_rows: config.n_rows,\n        batch_size: config.batch_size,\n      }),\n    });\n  }\n\n  // Results\n  async getResults(sessionId: string): Promise<GenerationResponse> {\n    return this.request<GenerationResult>(`/api/v1/sessions/${sessionId}/results`);\n  }\n\n  async getDataPreview(sessionId: string, limit: number = 10): Promise<ApiResponse<any>> {\n    return this.request(`/api/v1/sessions/${sessionId}/preview?limit=${limit}`);\n  }\n\n  async getQualityMetrics(sessionId: string): Promise<ApiResponse<any>> {\n    return this.request(`/api/v1/sessions/${sessionId}/quality`);\n  }\n\n  async downloadResults(sessionId: string): Promise<Blob | null> {\n    try {\n      const response = await fetch(`${this.baseUrl}/api/v1/sessions/${sessionId}/download`);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.blob();\n    } catch (error) {\n      console.error('Download failed:', error);\n      return null;\n    }\n  }\n\n  // Token Usage\n  async getTokenUsage(sessionId: string): Promise<TokenUsageResponse> {\n    return this.request<TokenUsage>(`/api/v1/sessions/${sessionId}/tokens`);\n  }\n\n  async getTotalTokenUsage(): Promise<TokenUsageResponse> {\n    return this.request<TokenUsage>('/api/v1/tokens/total');\n  }\n\n  // Complete Workflow (for testing)\n  async runCompleteWorkflow(\n    domain: string,\n    userContext: string,\n    nRows: number,\n    batchSize: number,\n    file: File\n  ): Promise<Blob | null> {\n    const formData = new FormData();\n    formData.append('domain', domain);\n    formData.append('user_context', userContext);\n    formData.append('n_rows', nRows.toString());\n    formData.append('batch_size', batchSize.toString());\n    formData.append('file', file);\n\n    try {\n      const response = await fetch(`${this.baseUrl}/api/v1/run-complete-workflow`, {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.blob();\n    } catch (error) {\n      console.error('Complete workflow failed:', error);\n      return null;\n    }\n  }\n}\n\nexport const apiService = new ApiService();\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AAEvB;;AAYA,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,yHAAA,CAAA,eAAY;IAC7B;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,EAAE;gBACzD,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,MAAc,EAAE,WAAmB,EAA4B;QACjF,OAAO,IAAI,CAAC,OAAO,CAAU,oBAAoB;YAC/C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAQ,cAAc;YAAY;QAC3D;IACF;IAEA,MAAM,WAAW,SAAiB,EAA4B;QAC5D,OAAO,IAAI,CAAC,OAAO,CAAU,CAAC,iBAAiB,EAAE,UAAU,OAAO,CAAC;IACrE;IAEA,MAAM,cAAc,SAAiB,EAAwB;QAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,WAAW,EAAE;YACnD,QAAQ;QACV;IACF;IAEA,cAAc;IACd,MAAM,WAAW,SAAiB,EAAE,IAAU,EAAwB;QACpE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,WAAW,CAAC,EAAE;gBACtF,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,gBACJ,SAAiB,EACjB,MAAwB,EACK;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAmB,CAAC,iBAAiB,EAAE,UAAU,SAAS,CAAC,EAAE;YAC9E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBACnB,QAAQ,OAAO,MAAM;gBACrB,YAAY,OAAO,UAAU;YAC/B;QACF;IACF;IAEA,UAAU;IACV,MAAM,WAAW,SAAiB,EAA+B;QAC/D,OAAO,IAAI,CAAC,OAAO,CAAmB,CAAC,iBAAiB,EAAE,UAAU,QAAQ,CAAC;IAC/E;IAEA,MAAM,eAAe,SAAiB,EAAE,QAAgB,EAAE,EAA6B;QACrF,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,eAAe,EAAE,OAAO;IAC5E;IAEA,MAAM,kBAAkB,SAAiB,EAA6B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,QAAQ,CAAC;IAC7D;IAEA,MAAM,gBAAgB,SAAiB,EAAwB;QAC7D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,UAAU,SAAS,CAAC;YAEpF,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;QACT;IACF;IAEA,cAAc;IACd,MAAM,cAAc,SAAiB,EAA+B;QAClE,OAAO,IAAI,CAAC,OAAO,CAAa,CAAC,iBAAiB,EAAE,UAAU,OAAO,CAAC;IACxE;IAEA,MAAM,qBAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAa;IAClC;IAEA,kCAAkC;IAClC,MAAM,oBACJ,MAAc,EACd,WAAmB,EACnB,KAAa,EACb,SAAiB,EACjB,IAAU,EACY;QACtB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAC1B,SAAS,MAAM,CAAC,gBAAgB;QAChC,SAAS,MAAM,CAAC,UAAU,MAAM,QAAQ;QACxC,SAAS,MAAM,CAAC,cAAc,UAAU,QAAQ;QAChD,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,EAAE;gBAC3E,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/services/websocket.ts"], "sourcesContent": ["// DataEcho WebSocket Service\n\nimport { WS_BASE_URL, WEBSOCKET_EVENTS } from '@/constants';\nimport type { WebSocketMessage } from '@/types';\n\ntype WebSocketEventHandler = (message: WebSocketMessage) => void;\n\nclass WebSocketService {\n  private ws: WebSocket | null = null;\n  private sessionId: string | null = null;\n  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();\n  private reconnectAttempts = 0;\n  private maxReconnectAttempts = 5;\n  private reconnectDelay = 1000;\n\n  connect(sessionId: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      try {\n        this.sessionId = sessionId;\n        this.ws = new WebSocket(`${WS_BASE_URL}/api/v1/ws/${sessionId}`);\n\n        this.ws.onopen = () => {\n          console.log(`WebSocket connected for session: ${sessionId}`);\n          this.reconnectAttempts = 0;\n          resolve();\n        };\n\n        this.ws.onmessage = (event) => {\n          try {\n            const message: WebSocketMessage = JSON.parse(event.data);\n            this.handleMessage(message);\n          } catch (error) {\n            console.error('Failed to parse WebSocket message:', error);\n          }\n        };\n\n        this.ws.onclose = (event) => {\n          console.log('WebSocket connection closed:', event.code, event.reason);\n          this.handleReconnect();\n        };\n\n        this.ws.onerror = (error) => {\n          console.error('WebSocket error:', error);\n          reject(error);\n        };\n      } catch (error) {\n        reject(error);\n      }\n    });\n  }\n\n  disconnect(): void {\n    if (this.ws) {\n      this.ws.close();\n      this.ws = null;\n    }\n    this.sessionId = null;\n    this.eventHandlers.clear();\n  }\n\n  private handleMessage(message: WebSocketMessage): void {\n    const handlers = this.eventHandlers.get(message.type) || [];\n    handlers.forEach(handler => {\n      try {\n        handler(message);\n      } catch (error) {\n        console.error('Error in WebSocket message handler:', error);\n      }\n    });\n  }\n\n  private handleReconnect(): void {\n    if (this.reconnectAttempts < this.maxReconnectAttempts && this.sessionId) {\n      this.reconnectAttempts++;\n      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n      \n      setTimeout(() => {\n        if (this.sessionId) {\n          this.connect(this.sessionId).catch(error => {\n            console.error('Reconnection failed:', error);\n          });\n        }\n      }, this.reconnectDelay * this.reconnectAttempts);\n    }\n  }\n\n  // Event Handlers\n  onStatusUpdate(handler: WebSocketEventHandler): () => void {\n    return this.addEventListener(WEBSOCKET_EVENTS.STATUS_UPDATE, handler);\n  }\n\n  onProgress(handler: WebSocketEventHandler): () => void {\n    return this.addEventListener(WEBSOCKET_EVENTS.PROGRESS, handler);\n  }\n\n  onCompletion(handler: WebSocketEventHandler): () => void {\n    return this.addEventListener(WEBSOCKET_EVENTS.COMPLETION, handler);\n  }\n\n  onError(handler: WebSocketEventHandler): () => void {\n    return this.addEventListener(WEBSOCKET_EVENTS.ERROR, handler);\n  }\n\n  private addEventListener(eventType: string, handler: WebSocketEventHandler): () => void {\n    if (!this.eventHandlers.has(eventType)) {\n      this.eventHandlers.set(eventType, []);\n    }\n    \n    this.eventHandlers.get(eventType)!.push(handler);\n\n    // Return cleanup function\n    return () => {\n      const handlers = this.eventHandlers.get(eventType);\n      if (handlers) {\n        const index = handlers.indexOf(handler);\n        if (index > -1) {\n          handlers.splice(index, 1);\n        }\n      }\n    };\n  }\n\n  // Send message to server (if needed)\n  send(message: any): void {\n    if (this.ws && this.ws.readyState === WebSocket.OPEN) {\n      this.ws.send(JSON.stringify(message));\n    } else {\n      console.warn('WebSocket is not connected');\n    }\n  }\n\n  // Connection status\n  get isConnected(): boolean {\n    return this.ws?.readyState === WebSocket.OPEN;\n  }\n\n  get connectionState(): string {\n    if (!this.ws) return 'disconnected';\n    \n    switch (this.ws.readyState) {\n      case WebSocket.CONNECTING: return 'connecting';\n      case WebSocket.OPEN: return 'connected';\n      case WebSocket.CLOSING: return 'closing';\n      case WebSocket.CLOSED: return 'disconnected';\n      default: return 'unknown';\n    }\n  }\n}\n\nexport const webSocketService = new WebSocketService();\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;AAE7B;;AAKA,MAAM;IACI,KAAuB,KAAK;IAC5B,YAA2B,KAAK;IAChC,gBAAsD,IAAI,MAAM;IAChE,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IACzB,iBAAiB,KAAK;IAE9B,QAAQ,SAAiB,EAAiB;QACxC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI;gBACF,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,GAAG,yHAAA,CAAA,cAAW,CAAC,WAAW,EAAE,WAAW;gBAE/D,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG;oBACf,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,WAAW;oBAC3D,IAAI,CAAC,iBAAiB,GAAG;oBACzB;gBACF;gBAEA,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;oBACnB,IAAI;wBACF,MAAM,UAA4B,KAAK,KAAK,CAAC,MAAM,IAAI;wBACvD,IAAI,CAAC,aAAa,CAAC;oBACrB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sCAAsC;oBACtD;gBACF;gBAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;oBACjB,QAAQ,GAAG,CAAC,gCAAgC,MAAM,IAAI,EAAE,MAAM,MAAM;oBACpE,IAAI,CAAC,eAAe;gBACtB;gBAEA,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,CAAC;oBACjB,QAAQ,KAAK,CAAC,oBAAoB;oBAClC,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,KAAK;YACb,IAAI,CAAC,EAAE,GAAG;QACZ;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEQ,cAAc,OAAyB,EAAQ;QACrD,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,IAAI,KAAK,EAAE;QAC3D,SAAS,OAAO,CAAC,CAAA;YACf,IAAI;gBACF,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;IACF;IAEQ,kBAAwB;QAC9B,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,SAAS,EAAE;YACxE,IAAI,CAAC,iBAAiB;YACtB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC;YAEjG,WAAW;gBACT,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA;wBACjC,QAAQ,KAAK,CAAC,wBAAwB;oBACxC;gBACF;YACF,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB;QACjD;IACF;IAEA,iBAAiB;IACjB,eAAe,OAA8B,EAAc;QACzD,OAAO,IAAI,CAAC,gBAAgB,CAAC,yHAAA,CAAA,mBAAgB,CAAC,aAAa,EAAE;IAC/D;IAEA,WAAW,OAA8B,EAAc;QACrD,OAAO,IAAI,CAAC,gBAAgB,CAAC,yHAAA,CAAA,mBAAgB,CAAC,QAAQ,EAAE;IAC1D;IAEA,aAAa,OAA8B,EAAc;QACvD,OAAO,IAAI,CAAC,gBAAgB,CAAC,yHAAA,CAAA,mBAAgB,CAAC,UAAU,EAAE;IAC5D;IAEA,QAAQ,OAA8B,EAAc;QAClD,OAAO,IAAI,CAAC,gBAAgB,CAAC,yHAAA,CAAA,mBAAgB,CAAC,KAAK,EAAE;IACvD;IAEQ,iBAAiB,SAAiB,EAAE,OAA8B,EAAc;QACtF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY;YACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,EAAE;QACtC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAY,IAAI,CAAC;QAExC,0BAA0B;QAC1B,OAAO;YACL,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACxC,IAAI,UAAU;gBACZ,MAAM,QAAQ,SAAS,OAAO,CAAC;gBAC/B,IAAI,QAAQ,CAAC,GAAG;oBACd,SAAS,MAAM,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA,qCAAqC;IACrC,KAAK,OAAY,EAAQ;QACvB,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YACpD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QAC9B,OAAO;YACL,QAAQ,IAAI,CAAC;QACf;IACF;IAEA,oBAAoB;IACpB,IAAI,cAAuB;QACzB,OAAO,IAAI,CAAC,EAAE,EAAE,eAAe,UAAU,IAAI;IAC/C;IAEA,IAAI,kBAA0B;QAC5B,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO;QAErB,OAAQ,IAAI,CAAC,EAAE,CAAC,UAAU;YACxB,KAAK,UAAU,UAAU;gBAAE,OAAO;YAClC,KAAK,UAAU,IAAI;gBAAE,OAAO;YAC5B,KAAK,UAAU,OAAO;gBAAE,OAAO;YAC/B,KAAK,UAAU,MAAM;gBAAE,OAAO;YAC9B;gBAAS,OAAO;QAClB;IACF;AACF;AAEO,MAAM,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 2735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/hooks/useSession.ts"], "sourcesContent": ["// DataEcho Session Management Hook\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { apiService } from '@/services/api';\nimport { webSocketService } from '@/services/websocket';\nimport type { Session, AgentStatus, TokenUsage } from '@/types';\nimport { AGENTS, AGENT_STATUS } from '@/constants';\n\nexport function useSession() {\n  const [session, setSession] = useState<Session | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [agents, setAgents] = useState<AgentStatus[]>([\n    {\n      name: AGENTS.PROFILER.name,\n      status: AGENT_STATUS.IDLE,\n      progress: 0,\n      message: 'Ready to analyze data',\n      tokens_used: 0,\n      icon: AGENTS.PROFILER.icon,\n      color: AGENTS.PROFILER.color\n    },\n    {\n      name: AGENTS.DEPENDENCY.name,\n      status: AGENT_STATUS.IDLE,\n      progress: 0,\n      message: 'Ready to map relationships',\n      tokens_used: 0,\n      icon: AGENTS.DEPENDENCY.icon,\n      color: AGENTS.DEPENDENCY.color\n    },\n    {\n      name: AGENTS.GENERATOR.name,\n      status: AGENT_STATUS.IDLE,\n      progress: 0,\n      message: 'Ready to generate data',\n      tokens_used: 0,\n      icon: AGENTS.GENERATOR.icon,\n      color: AGENTS.GENERATOR.color\n    }\n  ]);\n  const [tokenUsage, setTokenUsage] = useState<TokenUsage | null>(null);\n\n  const createSession = useCallback(async (domain: string, userContext: string) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await apiService.createSession(domain, userContext);\n      \n      if (response.success && response.data) {\n        setSession(response.data);\n        \n        // Connect WebSocket\n        await webSocketService.connect(response.data.session_id);\n        \n        // Set up WebSocket event handlers\n        setupWebSocketHandlers(response.data.session_id);\n        \n        return response.data;\n      } else {\n        throw new Error(response.error || 'Failed to create session');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  const setupWebSocketHandlers = useCallback((sessionId: string) => {\n    // Status updates\n    webSocketService.onStatusUpdate((message) => {\n      if (message.session_id === sessionId) {\n        setSession(prev => prev ? {\n          ...prev,\n          status: message.data.status,\n          current_step: message.data.current_step,\n          progress: message.data.progress,\n          message: message.data.message,\n          data: { ...prev.data, ...message.data.data }\n        } : null);\n\n        // Update agent status based on current step\n        updateAgentStatus(message.data.current_step, message.data.progress, message.data.message);\n      }\n    });\n\n    // Progress updates\n    webSocketService.onProgress((message) => {\n      if (message.session_id === sessionId) {\n        setSession(prev => prev ? {\n          ...prev,\n          progress: message.data.progress\n        } : null);\n      }\n    });\n\n    // Completion\n    webSocketService.onCompletion(async (message) => {\n      if (message.session_id === sessionId) {\n        try {\n          // Fetch enhanced results when generation completes\n          const resultsResponse = await apiService.getResults(sessionId);\n\n          setSession(prev => prev ? {\n            ...prev,\n            status: 'completed',\n            progress: 100,\n            message: 'Generation completed successfully',\n            results: resultsResponse.success ? resultsResponse.data : null\n          } : null);\n\n          // Mark all agents as completed\n          setAgents(prev => prev.map(agent => ({\n            ...agent,\n            status: AGENT_STATUS.COMPLETED,\n            progress: 100,\n            message: `${agent.name} completed successfully`\n          })));\n\n          // Fetch token usage\n          try {\n            const tokenResponse = await apiService.getTokenUsage(message.session_id);\n            if (tokenResponse.success) {\n              setTokenUsage(tokenResponse.data);\n\n              // Update individual agent token usage\n              setAgents(prev => prev.map(agent => {\n                let tokens = 0;\n                if (agent.name === 'Profiler Agent') {\n                  tokens = tokenResponse.data.profiler_tokens || 0;\n                } else if (agent.name === 'Dependency Agent') {\n                  tokens = tokenResponse.data.dependency_tokens || 0;\n                } else if (agent.name === 'Generator Agent') {\n                  tokens = tokenResponse.data.generator_tokens || 0;\n                }\n\n                return {\n                  ...agent,\n                  tokens_used: tokens\n                };\n              }));\n            }\n          } catch (error) {\n            console.error('Failed to fetch token usage:', error);\n          }\n        } catch (error) {\n          console.error('Failed to fetch results on completion:', error);\n          setSession(prev => prev ? {\n            ...prev,\n            status: 'completed',\n            progress: 100,\n            message: 'Generation completed successfully'\n          } : null);\n\n          // Mark all agents as completed even if results fetch fails\n          setAgents(prev => prev.map(agent => ({\n            ...agent,\n            status: AGENT_STATUS.COMPLETED,\n            progress: 100,\n            message: `${agent.name} completed successfully`\n          })));\n\n          // Fetch token usage even if results fetch fails\n          try {\n            const tokenResponse = await apiService.getTokenUsage(session.session_id);\n            if (tokenResponse.success) {\n              setTokenUsage(tokenResponse.data);\n            }\n          } catch (error) {\n            console.error('Failed to fetch token usage:', error);\n          }\n        }\n      }\n    });\n\n    // Errors\n    webSocketService.onError((message) => {\n      if (message.session_id === sessionId) {\n        setError(message.data.error || 'An error occurred');\n        setSession(prev => prev ? {\n          ...prev,\n          status: 'failed',\n          message: message.data.error || 'Generation failed'\n        } : null);\n      }\n    });\n  }, []);\n\n  const updateAgentStatus = useCallback((currentStep: string, progress: number, message: string) => {\n    setAgents(prev => prev.map(agent => {\n      switch (currentStep) {\n        case 'profiling':\n          if (agent.name === AGENTS.PROFILER.name) {\n            return {\n              ...agent,\n              status: progress >= 30 ? AGENT_STATUS.COMPLETED : AGENT_STATUS.RUNNING,\n              progress: Math.min(progress * 3.33, 100), // Scale to 100%\n              message: progress >= 30 ? 'Analysis completed' : message\n            };\n          }\n          break;\n        case 'dependency_analysis':\n          if (agent.name === AGENTS.DEPENDENCY.name) {\n            return {\n              ...agent,\n              status: progress >= 60 ? AGENT_STATUS.COMPLETED : AGENT_STATUS.RUNNING,\n              progress: Math.max(0, (progress - 30) * 3.33), // Scale from 30-60% to 0-100%\n              message: progress >= 60 ? 'Dependencies mapped' : message\n            };\n          }\n          break;\n        case 'generating':\n          if (agent.name === AGENTS.GENERATOR.name) {\n            return {\n              ...agent,\n              status: progress >= 95 ? AGENT_STATUS.COMPLETED : AGENT_STATUS.RUNNING,\n              progress: Math.max(0, (progress - 60) * 2.5), // Scale from 60-100% to 0-100%\n              message: progress >= 95 ? 'Generation completed' : message\n            };\n          }\n          break;\n      }\n      return agent;\n    }));\n  }, []);\n\n  const uploadFile = useCallback(async (file: File) => {\n    if (!session) throw new Error('No active session');\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await apiService.uploadFile(session.session_id, file);\n      \n      if (response.success) {\n        setSession(prev => prev ? {\n          ...prev,\n          csv_file_path: response.data?.file_path\n        } : null);\n        return response.data;\n      } else {\n        throw new Error(response.error || 'Upload failed');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Upload failed';\n      setError(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [session]);\n\n  const startGeneration = useCallback(async (nRows: number, batchSize: number) => {\n    if (!session) throw new Error('No active session');\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await apiService.startGeneration(session.session_id, {\n        domain: session.domain,\n        user_context: session.user_context,\n        n_rows: nRows,\n        batch_size: batchSize\n      });\n\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error || 'Generation failed to start');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Generation failed';\n      setError(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [session]);\n\n  const getDataPreview = useCallback(async (limit: number = 10) => {\n    if (!session) throw new Error('No active session');\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await apiService.getDataPreview(session.session_id, limit);\n\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error || 'Failed to get data preview');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [session]);\n\n  const getQualityMetrics = useCallback(async () => {\n    if (!session) throw new Error('No active session');\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await apiService.getQualityMetrics(session.session_id);\n\n      if (response.success) {\n        return response.data;\n      } else {\n        throw new Error(response.error || 'Failed to get quality metrics');\n      }\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      setError(errorMessage);\n      throw err;\n    } finally {\n      setLoading(false);\n    }\n  }, [session]);\n\n  const fetchTokenUsage = useCallback(async () => {\n    if (!session?.session_id) return;\n\n    try {\n      const response = await apiService.getTokenUsage(session.session_id);\n      if (response.success) {\n        setTokenUsage(response.data);\n\n        // Update individual agent token usage\n        setAgents(prev => prev.map(agent => {\n          let tokens = 0;\n          if (agent.name === 'Profiler Agent') {\n            tokens = response.data.profiler_tokens || 0;\n          } else if (agent.name === 'Dependency Agent') {\n            tokens = response.data.dependency_tokens || 0;\n          } else if (agent.name === 'Generator Agent') {\n            tokens = response.data.generator_tokens || 0;\n          }\n\n          return {\n            ...agent,\n            tokens_used: tokens\n          };\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to fetch token usage:', error);\n    }\n  }, [session]);\n\n  const cleanup = useCallback(() => {\n    webSocketService.disconnect();\n    setSession(null);\n    setAgents(prev => prev.map(agent => ({\n      ...agent,\n      status: AGENT_STATUS.IDLE,\n      progress: 0,\n      tokens_used: 0\n    })));\n    setTokenUsage(null);\n    setError(null);\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      cleanup();\n    };\n  }, [cleanup]);\n\n  return {\n    session,\n    agents,\n    tokenUsage,\n    loading,\n    error,\n    createSession,\n    uploadFile,\n    startGeneration,\n    getDataPreview,\n    getQualityMetrics,\n    fetchTokenUsage,\n    cleanup\n  };\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;AAEnC;AACA;AACA;AAEA;;;;;AAEO,SAAS;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAClD;YACE,MAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,IAAI;YAC1B,QAAQ,yHAAA,CAAA,eAAY,CAAC,IAAI;YACzB,UAAU;YACV,SAAS;YACT,aAAa;YACb,MAAM,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,IAAI;YAC1B,OAAO,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK;QAC9B;QACA;YACE,MAAM,yHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,IAAI;YAC5B,QAAQ,yHAAA,CAAA,eAAY,CAAC,IAAI;YACzB,UAAU;YACV,SAAS;YACT,aAAa;YACb,MAAM,yHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,IAAI;YAC5B,OAAO,yHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK;QAChC;QACA;YACE,MAAM,yHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,IAAI;YAC3B,QAAQ,yHAAA,CAAA,eAAY,CAAC,IAAI;YACzB,UAAU;YACV,SAAS;YACT,aAAa;YACb,MAAM,yHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,IAAI;YAC3B,OAAO,yHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,KAAK;QAC/B;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAEhE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAgB;QACvD,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,QAAQ;YAExD,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,WAAW,SAAS,IAAI;gBAExB,oBAAoB;gBACpB,MAAM,4HAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,UAAU;gBAEvD,kCAAkC;gBAClC,uBAAuB,SAAS,IAAI,CAAC,UAAU;gBAE/C,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,iBAAiB;QACjB,4HAAA,CAAA,mBAAgB,CAAC,cAAc,CAAC,CAAC;YAC/B,IAAI,QAAQ,UAAU,KAAK,WAAW;gBACpC,WAAW,CAAA,OAAQ,OAAO;wBACxB,GAAG,IAAI;wBACP,QAAQ,QAAQ,IAAI,CAAC,MAAM;wBAC3B,cAAc,QAAQ,IAAI,CAAC,YAAY;wBACvC,UAAU,QAAQ,IAAI,CAAC,QAAQ;wBAC/B,SAAS,QAAQ,IAAI,CAAC,OAAO;wBAC7B,MAAM;4BAAE,GAAG,KAAK,IAAI;4BAAE,GAAG,QAAQ,IAAI,CAAC,IAAI;wBAAC;oBAC7C,IAAI;gBAEJ,4CAA4C;gBAC5C,kBAAkB,QAAQ,IAAI,CAAC,YAAY,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE,QAAQ,IAAI,CAAC,OAAO;YAC1F;QACF;QAEA,mBAAmB;QACnB,4HAAA,CAAA,mBAAgB,CAAC,UAAU,CAAC,CAAC;YAC3B,IAAI,QAAQ,UAAU,KAAK,WAAW;gBACpC,WAAW,CAAA,OAAQ,OAAO;wBACxB,GAAG,IAAI;wBACP,UAAU,QAAQ,IAAI,CAAC,QAAQ;oBACjC,IAAI;YACN;QACF;QAEA,aAAa;QACb,4HAAA,CAAA,mBAAgB,CAAC,YAAY,CAAC,OAAO;YACnC,IAAI,QAAQ,UAAU,KAAK,WAAW;gBACpC,IAAI;oBACF,mDAAmD;oBACnD,MAAM,kBAAkB,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;oBAEpD,WAAW,CAAA,OAAQ,OAAO;4BACxB,GAAG,IAAI;4BACP,QAAQ;4BACR,UAAU;4BACV,SAAS;4BACT,SAAS,gBAAgB,OAAO,GAAG,gBAAgB,IAAI,GAAG;wBAC5D,IAAI;oBAEJ,+BAA+B;oBAC/B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;gCACnC,GAAG,KAAK;gCACR,QAAQ,yHAAA,CAAA,eAAY,CAAC,SAAS;gCAC9B,UAAU;gCACV,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;4BACjD,CAAC;oBAED,oBAAoB;oBACpB,IAAI;wBACF,MAAM,gBAAgB,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,QAAQ,UAAU;wBACvE,IAAI,cAAc,OAAO,EAAE;4BACzB,cAAc,cAAc,IAAI;4BAEhC,sCAAsC;4BACtC,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oCACzB,IAAI,SAAS;oCACb,IAAI,MAAM,IAAI,KAAK,kBAAkB;wCACnC,SAAS,cAAc,IAAI,CAAC,eAAe,IAAI;oCACjD,OAAO,IAAI,MAAM,IAAI,KAAK,oBAAoB;wCAC5C,SAAS,cAAc,IAAI,CAAC,iBAAiB,IAAI;oCACnD,OAAO,IAAI,MAAM,IAAI,KAAK,mBAAmB;wCAC3C,SAAS,cAAc,IAAI,CAAC,gBAAgB,IAAI;oCAClD;oCAEA,OAAO;wCACL,GAAG,KAAK;wCACR,aAAa;oCACf;gCACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,WAAW,CAAA,OAAQ,OAAO;4BACxB,GAAG,IAAI;4BACP,QAAQ;4BACR,UAAU;4BACV,SAAS;wBACX,IAAI;oBAEJ,2DAA2D;oBAC3D,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;gCACnC,GAAG,KAAK;gCACR,QAAQ,yHAAA,CAAA,eAAY,CAAC,SAAS;gCAC9B,UAAU;gCACV,SAAS,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;4BACjD,CAAC;oBAED,gDAAgD;oBAChD,IAAI;wBACF,MAAM,gBAAgB,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,QAAQ,UAAU;wBACvE,IAAI,cAAc,OAAO,EAAE;4BACzB,cAAc,cAAc,IAAI;wBAClC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;YACF;QACF;QAEA,SAAS;QACT,4HAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,CAAC;YACxB,IAAI,QAAQ,UAAU,KAAK,WAAW;gBACpC,SAAS,QAAQ,IAAI,CAAC,KAAK,IAAI;gBAC/B,WAAW,CAAA,OAAQ,OAAO;wBACxB,GAAG,IAAI;wBACP,QAAQ;wBACR,SAAS,QAAQ,IAAI,CAAC,KAAK,IAAI;oBACjC,IAAI;YACN;QACF;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,aAAqB,UAAkB;QAC5E,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBACzB,OAAQ;oBACN,KAAK;wBACH,IAAI,MAAM,IAAI,KAAK,yHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,IAAI,EAAE;4BACvC,OAAO;gCACL,GAAG,KAAK;gCACR,QAAQ,YAAY,KAAK,yHAAA,CAAA,eAAY,CAAC,SAAS,GAAG,yHAAA,CAAA,eAAY,CAAC,OAAO;gCACtE,UAAU,KAAK,GAAG,CAAC,WAAW,MAAM;gCACpC,SAAS,YAAY,KAAK,uBAAuB;4BACnD;wBACF;wBACA;oBACF,KAAK;wBACH,IAAI,MAAM,IAAI,KAAK,yHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,IAAI,EAAE;4BACzC,OAAO;gCACL,GAAG,KAAK;gCACR,QAAQ,YAAY,KAAK,yHAAA,CAAA,eAAY,CAAC,SAAS,GAAG,yHAAA,CAAA,eAAY,CAAC,OAAO;gCACtE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI;gCACxC,SAAS,YAAY,KAAK,wBAAwB;4BACpD;wBACF;wBACA;oBACF,KAAK;wBACH,IAAI,MAAM,IAAI,KAAK,yHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,IAAI,EAAE;4BACxC,OAAO;gCACL,GAAG,KAAK;gCACR,QAAQ,YAAY,KAAK,yHAAA,CAAA,eAAY,CAAC,SAAS,GAAG,yHAAA,CAAA,eAAY,CAAC,OAAO;gCACtE,UAAU,KAAK,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI;gCACxC,SAAS,YAAY,KAAK,yBAAyB;4BACrD;wBACF;wBACA;gBACJ;gBACA,OAAO;YACT;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAE9B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU,CAAC,QAAQ,UAAU,EAAE;YAEjE,IAAI,SAAS,OAAO,EAAE;gBACpB,WAAW,CAAA,OAAQ,OAAO;wBACxB,GAAG,IAAI;wBACP,eAAe,SAAS,IAAI,EAAE;oBAChC,IAAI;gBACJ,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,OAAe;QACxD,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAE9B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,eAAe,CAAC,QAAQ,UAAU,EAAE;gBACpE,QAAQ,QAAQ,MAAM;gBACtB,cAAc,QAAQ,YAAY;gBAClC,QAAQ;gBACR,YAAY;YACd;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAgB,EAAE;QAC1D,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAE9B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,QAAQ,UAAU,EAAE;YAErE,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;QAE9B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,QAAQ,UAAU;YAEtE,IAAI,SAAS,OAAO,EAAE;gBACpB,OAAO,SAAS,IAAI;YACtB,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,KAAK,IAAI;YACpC;QACF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,CAAC,SAAS,YAAY;QAE1B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC,QAAQ,UAAU;YAClE,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,SAAS,IAAI;gBAE3B,sCAAsC;gBACtC,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;wBACzB,IAAI,SAAS;wBACb,IAAI,MAAM,IAAI,KAAK,kBAAkB;4BACnC,SAAS,SAAS,IAAI,CAAC,eAAe,IAAI;wBAC5C,OAAO,IAAI,MAAM,IAAI,KAAK,oBAAoB;4BAC5C,SAAS,SAAS,IAAI,CAAC,iBAAiB,IAAI;wBAC9C,OAAO,IAAI,MAAM,IAAI,KAAK,mBAAmB;4BAC3C,SAAS,SAAS,IAAI,CAAC,gBAAgB,IAAI;wBAC7C;wBAEA,OAAO;4BACL,GAAG,KAAK;4BACR,aAAa;wBACf;oBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,4HAAA,CAAA,mBAAgB,CAAC,UAAU;QAC3B,WAAW;QACX,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;oBACnC,GAAG,KAAK;oBACR,QAAQ,yHAAA,CAAA,eAAY,CAAC,IAAI;oBACzB,UAAU;oBACV,aAAa;gBACf,CAAC;QACD,cAAc;QACd,SAAS;IACX,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/features/ResultsDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { motion } from 'motion/react';\nimport { Download, Eye, BarChart3, CheckCircle, AlertCircle, Zap, DollarSign, Plus } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { Progress } from '@/components/ui/progress';\nimport { useSession } from '@/hooks/useSession';\nimport { apiService } from '@/services/api';\n\ninterface ResultsDisplayProps {\n  sessionId: string;\n  onStartNewSession?: () => void;\n}\n\nexport function ResultsDisplay({ sessionId, onStartNewSession }: ResultsDisplayProps) {\n  const { session, tokenUsage } = useSession();\n  const [previewData, setPreviewData] = useState<any>(null);\n  const [qualityMetrics, setQualityMetrics] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('preview');\n\n  const loadResultsData = useCallback(async () => {\n    setLoading(true);\n    try {\n      // Load preview data and quality metrics\n      const [previewResponse, qualityResponse] = await Promise.all([\n        apiService.getDataPreview(sessionId, 10),\n        apiService.getQualityMetrics(sessionId)\n      ]);\n\n      if (previewResponse.success) {\n        setPreviewData(previewResponse.data);\n      }\n\n      if (qualityResponse.success) {\n        setQualityMetrics(qualityResponse.data);\n      }\n    } catch (error) {\n      console.error('Failed to load results data:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [sessionId]);\n\n  useEffect(() => {\n    // Load results data immediately when component mounts with a sessionId\n    // Don't wait for session status since session might be undefined after completion\n    if (sessionId) {\n      loadResultsData();\n    }\n  }, [sessionId, loadResultsData]);\n\n  const handleDownload = async () => {\n    try {\n      const blob = await apiService.downloadResults(sessionId);\n      if (blob) {\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.style.display = 'none';\n        a.href = url;\n        a.download = `dataecho_results_${sessionId}.csv`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      }\n    } catch (error) {\n      console.error('Download failed:', error);\n    }\n  };\n\n  if (!session || session.status !== 'completed') {\n    return null;\n  }\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"space-y-6\"\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-2\">\n          <CheckCircle className=\"h-6 w-6 text-green-500\" />\n          <h2 className=\"text-2xl font-semibold\">Generation Complete!</h2>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Button onClick={handleDownload} className=\"flex items-center space-x-2\">\n            <Download className=\"h-4 w-4\" />\n            <span>Download CSV</span>\n          </Button>\n          {onStartNewSession && (\n            <Button\n              onClick={onStartNewSession}\n              variant=\"outline\"\n              className=\"flex items-center space-x-2\"\n            >\n              <Plus className=\"h-4 w-4\" />\n              <span>New Session</span>\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      {qualityMetrics && (\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <BarChart3 className=\"h-4 w-4 text-blue-500\" />\n                <div>\n                  <p className=\"text-sm font-medium\">Total Rows</p>\n                  <p className=\"text-2xl font-bold\">{qualityMetrics.total_rows}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                <div>\n                  <p className=\"text-sm font-medium\">Quality Score</p>\n                  <p className=\"text-2xl font-bold\">{Math.round(qualityMetrics.overall_quality * 100)}%</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Eye className=\"h-4 w-4 text-purple-500\" />\n                <div>\n                  <p className=\"text-sm font-medium\">Columns</p>\n                  <p className=\"text-2xl font-bold\">{qualityMetrics.columns_count}</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n          \n          <Card>\n            <CardContent className=\"p-4\">\n              <div className=\"flex items-center space-x-2\">\n                <AlertCircle className=\"h-4 w-4 text-orange-500\" />\n                <div>\n                  <p className=\"text-sm font-medium\">Completion</p>\n                  <p className=\"text-2xl font-bold\">{Math.round(qualityMetrics.generation_performance?.completion_rate || 100)}%</p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      )}\n\n      {/* Detailed Results */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Generated Data</CardTitle>\n          <CardDescription>\n            Preview and analyze your synthetic data\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Tabs value={activeTab} onValueChange={setActiveTab}>\n            <TabsList className=\"grid w-full grid-cols-4\">\n              <TabsTrigger value=\"preview\">Data Preview</TabsTrigger>\n              <TabsTrigger value=\"quality\">Quality Metrics</TabsTrigger>\n              <TabsTrigger value=\"tokens\">Token Usage</TabsTrigger>\n              <TabsTrigger value=\"insights\">AI Insights</TabsTrigger>\n            </TabsList>\n            \n            <TabsContent value=\"preview\" className=\"space-y-4\">\n              {loading ? (\n                <div className=\"flex items-center justify-center py-8\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n                </div>\n              ) : previewData ? (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <p className=\"text-sm text-muted-foreground\">\n                      Showing {previewData.preview_rows} of {previewData.total_rows} rows\n                    </p>\n                    <Badge variant=\"outline\">\n                      {previewData.columns?.length} columns\n                    </Badge>\n                  </div>\n                  \n                  <div className=\"border rounded-lg overflow-hidden\">\n                    <Table>\n                      <TableHeader>\n                        <TableRow>\n                          {previewData.columns?.map((column: string) => (\n                            <TableHead key={column}>{column}</TableHead>\n                          ))}\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {previewData.data?.map((row: any, index: number) => (\n                          <TableRow key={index}>\n                            {previewData.columns?.map((column: string) => (\n                              <TableCell key={column}>\n                                {row[column]?.toString() || '-'}\n                              </TableCell>\n                            ))}\n                          </TableRow>\n                        ))}\n                      </TableBody>\n                    </Table>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  No preview data available\n                </div>\n              )}\n            </TabsContent>\n            \n            <TabsContent value=\"quality\" className=\"space-y-4\">\n              {qualityMetrics && (\n                <div className=\"space-y-6\">\n                  {/* Overall Quality */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className=\"font-medium\">Overall Quality</h4>\n                      <span className=\"text-sm font-medium\">{Math.round(qualityMetrics.overall_quality * 100)}%</span>\n                    </div>\n                    <Progress value={qualityMetrics.overall_quality * 100} className=\"h-2\" />\n                  </div>\n                  \n                  {/* Data Consistency */}\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <h4 className=\"font-medium\">Data Consistency</h4>\n                      <span className=\"text-sm font-medium\">{Math.round(qualityMetrics.data_consistency * 100)}%</span>\n                    </div>\n                    <Progress value={qualityMetrics.data_consistency * 100} className=\"h-2\" />\n                  </div>\n                  \n                  {/* Generation Performance */}\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <Card>\n                      <CardHeader className=\"pb-2\">\n                        <CardTitle className=\"text-sm\">Generation Time</CardTitle>\n                      </CardHeader>\n                      <CardContent>\n                        <p className=\"text-lg font-semibold\">{qualityMetrics.generation_time}</p>\n                      </CardContent>\n                    </Card>\n                    \n                    <Card>\n                      <CardHeader className=\"pb-2\">\n                        <CardTitle className=\"text-sm\">Relationships Found</CardTitle>\n                      </CardHeader>\n                      <CardContent>\n                        <p className=\"text-lg font-semibold\">{qualityMetrics.dependency_analysis?.relationships_found || 0}</p>\n                      </CardContent>\n                    </Card>\n                  </div>\n                </div>\n              )}\n            </TabsContent>\n\n            <TabsContent value=\"tokens\" className=\"space-y-4\">\n              {tokenUsage ? (\n                <div className=\"space-y-4\">\n                  {/* Total Usage Summary */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle className=\"flex items-center gap-2\">\n                        <Zap className=\"h-5 w-5 text-green-500\" />\n                        Total Token Usage\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div className=\"text-center\">\n                          <p className=\"text-2xl font-bold text-green-600\">{tokenUsage.total_tokens.toLocaleString()}</p>\n                          <p className=\"text-sm text-muted-foreground\">Total Tokens</p>\n                        </div>\n                        <div className=\"text-center\">\n                          <p className=\"text-2xl font-bold text-blue-600\">${tokenUsage.cost_estimate.toFixed(4)}</p>\n                          <p className=\"text-sm text-muted-foreground\">Estimated Cost</p>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n\n                  {/* Agent-Level Breakdown */}\n                  <Card>\n                    <CardHeader>\n                      <CardTitle className=\"flex items-center gap-2\">\n                        <BarChart3 className=\"h-5 w-5 text-blue-500\" />\n                        Agent Breakdown\n                      </CardTitle>\n                      <CardDescription>Token usage by individual AI agents</CardDescription>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-4\">\n                        {/* Profiler Agent */}\n                        <div className=\"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"text-2xl\">📊</div>\n                            <div>\n                              <p className=\"font-medium\">Profiler Agent</p>\n                              <p className=\"text-sm text-muted-foreground\">Data analysis & pattern detection</p>\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <p className=\"font-semibold\">{tokenUsage.profiler_tokens.toLocaleString()}</p>\n                            <p className=\"text-sm text-muted-foreground\">tokens</p>\n                          </div>\n                        </div>\n\n                        {/* Dependency Agent */}\n                        <div className=\"flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"text-2xl\">🔗</div>\n                            <div>\n                              <p className=\"font-medium\">Dependency Agent</p>\n                              <p className=\"text-sm text-muted-foreground\">Relationship mapping</p>\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <p className=\"font-semibold\">{tokenUsage.dependency_tokens.toLocaleString()}</p>\n                            <p className=\"text-sm text-muted-foreground\">tokens</p>\n                          </div>\n                        </div>\n\n                        {/* Generator Agent */}\n                        <div className=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                          <div className=\"flex items-center gap-3\">\n                            <div className=\"text-2xl\">⚡</div>\n                            <div>\n                              <p className=\"font-medium\">Generator Agent</p>\n                              <p className=\"text-sm text-muted-foreground\">Synthetic data generation</p>\n                            </div>\n                          </div>\n                          <div className=\"text-right\">\n                            <p className=\"font-semibold\">{tokenUsage.generator_tokens.toLocaleString()}</p>\n                            <p className=\"text-sm text-muted-foreground\">tokens</p>\n                          </div>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center py-8\">\n                  <div className=\"text-center\">\n                    <AlertCircle className=\"h-8 w-8 text-muted-foreground mx-auto mb-2\" />\n                    <p className=\"text-muted-foreground\">Token usage data not available</p>\n                  </div>\n                </div>\n              )}\n            </TabsContent>\n\n            <TabsContent value=\"insights\" className=\"space-y-4\">\n              {session?.results && (\n                <div className=\"space-y-4\">\n                  <Card>\n                    <CardHeader>\n                      <CardTitle className=\"text-sm\">Profiler Insights</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <p className=\"text-sm text-muted-foreground\">\n                        {session.results.generation_summary?.profiler_insights || 'No insights available'}\n                      </p>\n                    </CardContent>\n                  </Card>\n                  \n                  <Card>\n                    <CardHeader>\n                      <CardTitle className=\"text-sm\">Data Types Detected</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"space-y-2\">\n                        {qualityMetrics?.profiler_insights?.data_types && \n                          Object.entries(qualityMetrics.profiler_insights.data_types).map(([column, type]) => (\n                            <div key={column} className=\"flex items-center justify-between\">\n                              <span className=\"text-sm\">{column}</span>\n                              <Badge variant=\"secondary\">{type as string}</Badge>\n                            </div>\n                          ))\n                        }\n                      </div>\n                    </CardContent>\n                  </Card>\n                </div>\n              )}\n            </TabsContent>\n          </Tabs>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAmBO,SAAS,eAAe,EAAE,SAAS,EAAE,iBAAiB,EAAuB;IAClF,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,WAAW;QACX,IAAI;YACF,wCAAwC;YACxC,MAAM,CAAC,iBAAiB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3D,sHAAA,CAAA,aAAU,CAAC,cAAc,CAAC,WAAW;gBACrC,sHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC;aAC9B;YAED,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,eAAe,gBAAgB,IAAI;YACrC;YAEA,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,kBAAkB,gBAAgB,IAAI;YACxC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uEAAuE;QACvE,kFAAkF;QAClF,IAAI,WAAW;YACb;QACF;IACF,GAAG;QAAC;QAAW;KAAgB;IAE/B,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,OAAO,MAAM,sHAAA,CAAA,aAAU,CAAC,eAAe,CAAC;YAC9C,IAAI,MAAM;gBACR,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,KAAK,CAAC,OAAO,GAAG;gBAClB,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE,UAAU,IAAI,CAAC;gBAChD,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oBAAoB;QACpC;IACF;IAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,aAAa;QAC9C,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;;;;;;;kCAEzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS;gCAAgB,WAAU;;kDACzC,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;4BAEP,mCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAOb,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAsB,eAAe,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;;oDAAsB,KAAK,KAAK,CAAC,eAAe,eAAe,GAAG;oDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM5F,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;0DAAsB,eAAe,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMvE,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,8OAAC;gDAAE,WAAU;;oDAAsB,KAAK,KAAK,CAAC,eAAe,sBAAsB,EAAE,mBAAmB;oDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzH,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;;8CACrC,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;sDAC7B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAU;;;;;;sDAC7B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAS;;;;;;sDAC5B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;sDAAW;;;;;;;;;;;;8CAGhC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,wBACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;+CAEf,4BACF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAgC;4DAClC,YAAY,YAAY;4DAAC;4DAAK,YAAY,UAAU;4DAAC;;;;;;;kEAEhE,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;;4DACZ,YAAY,OAAO,EAAE;4DAAO;;;;;;;;;;;;;0DAIjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sEACJ,8OAAC,iIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;0EACN,YAAY,OAAO,EAAE,IAAI,CAAC,uBACzB,8OAAC,iIAAA,CAAA,YAAS;kFAAe;uEAAT;;;;;;;;;;;;;;;sEAItB,8OAAC,iIAAA,CAAA,YAAS;sEACP,YAAY,IAAI,EAAE,IAAI,CAAC,KAAU,sBAChC,8OAAC,iIAAA,CAAA,WAAQ;8EACN,YAAY,OAAO,EAAE,IAAI,CAAC,uBACzB,8OAAC,iIAAA,CAAA,YAAS;sFACP,GAAG,CAAC,OAAO,EAAE,cAAc;2EADd;;;;;mEAFL;;;;;;;;;;;;;;;;;;;;;;;;;;6DAazB,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;;;;;;8CAM5D,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,gCACC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAK,WAAU;;oEAAuB,KAAK,KAAK,CAAC,eAAe,eAAe,GAAG;oEAAK;;;;;;;;;;;;;kEAE1F,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO,eAAe,eAAe,GAAG;wDAAK,WAAU;;;;;;;;;;;;0DAInE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,8OAAC;gEAAK,WAAU;;oEAAuB,KAAK,KAAK,CAAC,eAAe,gBAAgB,GAAG;oEAAK;;;;;;;;;;;;;kEAE3F,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO,eAAe,gBAAgB,GAAG;wDAAK,WAAU;;;;;;;;;;;;0DAIpE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gIAAA,CAAA,OAAI;;0EACH,8OAAC,gIAAA,CAAA,aAAU;gEAAC,WAAU;0EACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAU;;;;;;;;;;;0EAEjC,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC;oEAAE,WAAU;8EAAyB,eAAe,eAAe;;;;;;;;;;;;;;;;;kEAIxE,8OAAC,gIAAA,CAAA,OAAI;;0EACH,8OAAC,gIAAA,CAAA,aAAU;gEAAC,WAAU;0EACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAU;;;;;;;;;;;0EAEjC,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC;oEAAE,WAAU;8EAAyB,eAAe,mBAAmB,EAAE,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ7G,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAS,WAAU;8CACnC,2BACC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAA2B;;;;;;;;;;;;kEAI9C,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAqC,WAAW,YAAY,CAAC,cAAc;;;;;;sFACxF,8OAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;8EAE/C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;gFAAmC;gFAAE,WAAW,aAAa,CAAC,OAAO,CAAC;;;;;;;sFACnF,8OAAC;4EAAE,WAAU;sFAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOrD,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;;0EACT,8OAAC,gIAAA,CAAA,YAAS;gEAAC,WAAU;;kFACnB,8OAAC,kNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAA0B;;;;;;;0EAGjD,8OAAC,gIAAA,CAAA,kBAAe;0EAAC;;;;;;;;;;;;kEAEnB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FAAW;;;;;;8FAC1B,8OAAC;;sGACC,8OAAC;4FAAE,WAAU;sGAAc;;;;;;sGAC3B,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;;;;;;;sFAGjD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;8FAAiB,WAAW,eAAe,CAAC,cAAc;;;;;;8FACvE,8OAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;;;;;;;8EAKjD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FAAW;;;;;;8FAC1B,8OAAC;;sGACC,8OAAC;4FAAE,WAAU;sGAAc;;;;;;sGAC3B,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;;;;;;;sFAGjD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;8FAAiB,WAAW,iBAAiB,CAAC,cAAc;;;;;;8FACzE,8OAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;;;;;;;8EAKjD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FAAW;;;;;;8FAC1B,8OAAC;;sGACC,8OAAC;4FAAE,WAAU;sGAAc;;;;;;sGAC3B,8OAAC;4FAAE,WAAU;sGAAgC;;;;;;;;;;;;;;;;;;sFAGjD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAE,WAAU;8FAAiB,WAAW,gBAAgB,CAAC,cAAc;;;;;;8FACxE,8OAAC;oFAAE,WAAU;8FAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAQzD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;8CAM7C,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACrC,SAAS,yBACR,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAU;;;;;;;;;;;kEAEjC,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAE,WAAU;sEACV,QAAQ,OAAO,CAAC,kBAAkB,EAAE,qBAAqB;;;;;;;;;;;;;;;;;0DAKhE,8OAAC,gIAAA,CAAA,OAAI;;kEACH,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAU;;;;;;;;;;;kEAEjC,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;sEACZ,gBAAgB,mBAAmB,cAClC,OAAO,OAAO,CAAC,eAAe,iBAAiB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,KAAK,iBAC7E,8OAAC;oEAAiB,WAAU;;sFAC1B,8OAAC;4EAAK,WAAU;sFAAW;;;;;;sFAC3B,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;sFAAa;;;;;;;mEAFpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBtC", "debugId": null}}, {"offset": {"line": 4492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/components/ui/loading-animations.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'motion/react';\n\n// Pulsing dots loader\nexport function PulsingDots({ size = 'md', color = 'blue' }: { size?: 'sm' | 'md' | 'lg', color?: string }) {\n  const sizeClasses = {\n    sm: 'w-1 h-1',\n    md: 'w-2 h-2', \n    lg: 'w-3 h-3'\n  };\n\n  const colorClasses = {\n    blue: 'bg-blue-500',\n    green: 'bg-green-500',\n    purple: 'bg-purple-500',\n    orange: 'bg-orange-500'\n  };\n\n  return (\n    <div className=\"flex items-center space-x-1\">\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className={`${sizeClasses[size]} ${colorClasses[color as keyof typeof colorClasses] || 'bg-blue-500'} rounded-full`}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7]\n          }}\n          transition={{\n            duration: 0.6,\n            repeat: Infinity,\n            delay: index * 0.1\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n\n// Spinning loader\nexport function SpinningLoader({ size = 'md', color = 'blue' }: { size?: 'sm' | 'md' | 'lg', color?: string }) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  const colorClasses = {\n    blue: 'border-blue-500',\n    green: 'border-green-500',\n    purple: 'border-purple-500',\n    orange: 'border-orange-500'\n  };\n\n  return (\n    <motion.div\n      className={`${sizeClasses[size]} border-2 ${colorClasses[color as keyof typeof colorClasses] || 'border-blue-500'} border-t-transparent rounded-full`}\n      animate={{ rotate: 360 }}\n      transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n    />\n  );\n}\n\n// Wave loader\nexport function WaveLoader({ color = 'blue' }: { color?: string }) {\n  const colorClasses = {\n    blue: 'bg-blue-500',\n    green: 'bg-green-500',\n    purple: 'bg-purple-500',\n    orange: 'bg-orange-500'\n  };\n\n  return (\n    <div className=\"flex items-end space-x-1\">\n      {[0, 1, 2, 3, 4].map((index) => (\n        <motion.div\n          key={index}\n          className={`w-1 ${colorClasses[color as keyof typeof colorClasses] || 'bg-blue-500'} rounded-full`}\n          animate={{\n            height: [4, 16, 4]\n          }}\n          transition={{\n            duration: 0.8,\n            repeat: Infinity,\n            delay: index * 0.1\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n\n// Typing indicator\nexport function TypingIndicator() {\n  return (\n    <div className=\"flex items-center space-x-1\">\n      <span className=\"text-sm text-muted-foreground\">AI is thinking</span>\n      <div className=\"flex space-x-1\">\n        {[0, 1, 2].map((index) => (\n          <motion.div\n            key={index}\n            className=\"w-1 h-1 bg-muted-foreground rounded-full\"\n            animate={{\n              opacity: [0.4, 1, 0.4]\n            }}\n            transition={{\n              duration: 1,\n              repeat: Infinity,\n              delay: index * 0.2\n            }}\n          />\n        ))}\n      </div>\n    </div>\n  );\n}\n\n// Progress ring\nexport function ProgressRing({ progress, size = 'md', color = 'blue' }: { \n  progress: number, \n  size?: 'sm' | 'md' | 'lg',\n  color?: string \n}) {\n  const sizeClasses = {\n    sm: { width: 32, height: 32, strokeWidth: 2 },\n    md: { width: 48, height: 48, strokeWidth: 3 },\n    lg: { width: 64, height: 64, strokeWidth: 4 }\n  };\n\n  const colorClasses = {\n    blue: 'stroke-blue-500',\n    green: 'stroke-green-500',\n    purple: 'stroke-purple-500',\n    orange: 'stroke-orange-500'\n  };\n\n  const { width, height, strokeWidth } = sizeClasses[size];\n  const radius = (width - strokeWidth * 2) / 2;\n  const circumference = radius * 2 * Math.PI;\n  const strokeDasharray = circumference;\n  const strokeDashoffset = circumference - (progress / 100) * circumference;\n\n  return (\n    <div className=\"relative\">\n      <svg width={width} height={height} className=\"transform -rotate-90\">\n        {/* Background circle */}\n        <circle\n          cx={width / 2}\n          cy={height / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth={strokeWidth}\n          fill=\"none\"\n          className=\"text-muted-foreground/20\"\n        />\n        {/* Progress circle */}\n        <motion.circle\n          cx={width / 2}\n          cy={height / 2}\n          r={radius}\n          stroke=\"currentColor\"\n          strokeWidth={strokeWidth}\n          fill=\"none\"\n          className={colorClasses[color as keyof typeof colorClasses] || 'stroke-blue-500'}\n          strokeLinecap=\"round\"\n          strokeDasharray={strokeDasharray}\n          initial={{ strokeDashoffset: circumference }}\n          animate={{ strokeDashoffset }}\n          transition={{ duration: 0.5, ease: \"easeOut\" }}\n        />\n      </svg>\n      {/* Progress text */}\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <span className=\"text-xs font-medium\">{Math.round(progress)}%</span>\n      </div>\n    </div>\n  );\n}\n\n// Skeleton loader\nexport function SkeletonLoader({ className = \"\" }: { className?: string }) {\n  return (\n    <motion.div\n      className={`bg-muted rounded ${className}`}\n      animate={{\n        opacity: [0.5, 1, 0.5]\n      }}\n      transition={{\n        duration: 1.5,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }}\n    />\n  );\n}\n\n// Success checkmark animation\nexport function SuccessCheckmark({ size = 'md', color = 'green' }: { size?: 'sm' | 'md' | 'lg', color?: string }) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  const colorClasses = {\n    green: 'text-green-500',\n    blue: 'text-blue-500',\n    purple: 'text-purple-500'\n  };\n\n  return (\n    <motion.div\n      className={`${sizeClasses[size]} ${colorClasses[color as keyof typeof colorClasses] || 'text-green-500'} flex items-center justify-center`}\n      initial={{ scale: 0, rotate: -180 }}\n      animate={{ scale: 1, rotate: 0 }}\n      transition={{ \n        type: \"spring\",\n        stiffness: 200,\n        damping: 10\n      }}\n    >\n      <svg\n        fill=\"none\"\n        stroke=\"currentColor\"\n        viewBox=\"0 0 24 24\"\n        className=\"w-full h-full\"\n      >\n        <motion.path\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          strokeWidth={2}\n          d=\"M5 13l4 4L19 7\"\n          initial={{ pathLength: 0 }}\n          animate={{ pathLength: 1 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n        />\n      </svg>\n    </motion.div>\n  );\n}\n\n// Floating particles effect\nexport function FloatingParticles({ count = 20 }: { count?: number }) {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n      {Array.from({ length: count }).map((_, index) => (\n        <motion.div\n          key={index}\n          className=\"absolute w-1 h-1 bg-blue-500/20 rounded-full\"\n          initial={{\n            x: Math.random() * window.innerWidth,\n            y: window.innerHeight + 10,\n            opacity: 0\n          }}\n          animate={{\n            y: -10,\n            opacity: [0, 1, 0]\n          }}\n          transition={{\n            duration: Math.random() * 3 + 2,\n            repeat: Infinity,\n            delay: Math.random() * 2\n          }}\n        />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAFA;;;AAKO,SAAS,YAAY,EAAE,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAiD;IACxG,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAmC,IAAI,cAAc,aAAa,CAAC;gBACnH,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;eAVK;;;;;;;;;;AAef;AAGO,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAiD;IAC3G,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,MAAmC,IAAI,kBAAkB,kCAAkC,CAAC;QACrJ,SAAS;YAAE,QAAQ;QAAI;QACvB,YAAY;YAAE,UAAU;YAAG,QAAQ;YAAU,MAAM;QAAS;;;;;;AAGlE;AAGO,SAAS,WAAW,EAAE,QAAQ,MAAM,EAAsB;IAC/D,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ;YAAC;YAAG;YAAG;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,CAAC,IAAI,EAAE,YAAY,CAAC,MAAmC,IAAI,cAAc,aAAa,CAAC;gBAClG,SAAS;oBACP,QAAQ;wBAAC;wBAAG;wBAAI;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,OAAO,QAAQ;gBACjB;eATK;;;;;;;;;;AAcf;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;0BAAgC;;;;;;0BAChD,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,OAAO,QAAQ;wBACjB;uBATK;;;;;;;;;;;;;;;;AAejB;AAGO,SAAS,aAAa,EAAE,QAAQ,EAAE,OAAO,IAAI,EAAE,QAAQ,MAAM,EAInE;IACC,MAAM,cAAc;QAClB,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,aAAa;QAAE;QAC5C,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,aAAa;QAAE;QAC5C,IAAI;YAAE,OAAO;YAAI,QAAQ;YAAI,aAAa;QAAE;IAC9C;IAEA,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC,KAAK;IACxD,MAAM,SAAS,CAAC,QAAQ,cAAc,CAAC,IAAI;IAC3C,MAAM,gBAAgB,SAAS,IAAI,KAAK,EAAE;IAC1C,MAAM,kBAAkB;IACxB,MAAM,mBAAmB,gBAAgB,AAAC,WAAW,MAAO;IAE5D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,OAAO;gBAAO,QAAQ;gBAAQ,WAAU;;kCAE3C,8OAAC;wBACC,IAAI,QAAQ;wBACZ,IAAI,SAAS;wBACb,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,WAAU;;;;;;kCAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,IAAI,QAAQ;wBACZ,IAAI,SAAS;wBACb,GAAG;wBACH,QAAO;wBACP,aAAa;wBACb,MAAK;wBACL,WAAW,YAAY,CAAC,MAAmC,IAAI;wBAC/D,eAAc;wBACd,iBAAiB;wBACjB,SAAS;4BAAE,kBAAkB;wBAAc;wBAC3C,SAAS;4BAAE;wBAAiB;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAU;;;;;;;;;;;;0BAIjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;wBAAuB,KAAK,KAAK,CAAC;wBAAU;;;;;;;;;;;;;;;;;;AAIpE;AAGO,SAAS,eAAe,EAAE,YAAY,EAAE,EAA0B;IACvE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,iBAAiB,EAAE,WAAW;QAC1C,SAAS;YACP,SAAS;gBAAC;gBAAK;gBAAG;aAAI;QACxB;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;;;;;AAGN;AAGO,SAAS,iBAAiB,EAAE,OAAO,IAAI,EAAE,QAAQ,OAAO,EAAiD;IAC9G,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,OAAO;QACP,MAAM;QACN,QAAQ;IACV;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAmC,IAAI,iBAAiB,iCAAiC,CAAC;QAC1I,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC/B,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;kBAEA,cAAA,8OAAC;YACC,MAAK;YACL,QAAO;YACP,SAAQ;YACR,WAAU;sBAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;gBACF,SAAS;oBAAE,YAAY;gBAAE;gBACzB,SAAS;oBAAE,YAAY;gBAAE;gBACzB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;;;;;;;;;;;;;;;AAKlD;AAGO,SAAS,kBAAkB,EAAE,QAAQ,EAAE,EAAsB;IAClE,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,SAAS;oBACP,GAAG,KAAK,MAAM,KAAK,OAAO,UAAU;oBACpC,GAAG,OAAO,WAAW,GAAG;oBACxB,SAAS;gBACX;gBACA,SAAS;oBACP,GAAG,CAAC;oBACJ,SAAS;wBAAC;wBAAG;wBAAG;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU,KAAK,MAAM,KAAK,IAAI;oBAC9B,QAAQ;oBACR,OAAO,KAAK,MAAM,KAAK;gBACzB;eAfK;;;;;;;;;;AAoBf", "debugId": null}}, {"offset": {"line": 4903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Git/data-echo/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'motion/react';\nimport { Header } from '@/components/features/Header';\nimport { FileUpload } from '@/components/features/FileUpload';\nimport { AgentStatusCard } from '@/components/features/AgentStatusCard';\nimport { ConfigurationForm } from '@/components/features/ConfigurationForm';\nimport { ResultsDisplay } from '@/components/features/ResultsDisplay';\nimport { PulsingDots, TypingIndicator, FloatingParticles } from '@/components/ui/loading-animations';\nimport { useSession } from '@/hooks/useSession';\nimport { apiService } from '@/services/api';\nimport type { FileUpload as FileUploadType, GenerationConfig } from '@/types';\n\nexport default function Home() {\n  const {\n    session,\n    agents,\n    tokenUsage,\n    loading,\n    error,\n    createSession,\n    uploadFile,\n    startGeneration,\n    fetchTokenUsage\n  } = useSession();\n\n  const [uploadedFile, setUploadedFile] = useState<FileUploadType | null>(null);\n  const [currentStep, setCurrentStep] = useState<'upload' | 'configure' | 'processing' | 'results'>('upload');\n\n  const handleFileSelect = async (file: File) => {\n    const fileUpload: FileUploadType = {\n      file,\n      name: file.name,\n      size: file.size,\n      type: file.type,\n      progress: 0,\n      status: 'pending'\n    };\n\n    setUploadedFile(fileUpload);\n\n    try {\n      // Simulate upload progress\n      setUploadedFile(prev => prev ? { ...prev, status: 'uploading', progress: 0 } : null);\n      \n      // Simulate progress updates\n      for (let i = 0; i <= 100; i += 10) {\n        await new Promise(resolve => setTimeout(resolve, 100));\n        setUploadedFile(prev => prev ? { ...prev, progress: i } : null);\n      }\n\n      setUploadedFile(prev => prev ? { ...prev, status: 'completed', progress: 100 } : null);\n      setCurrentStep('configure');\n    } catch (err) {\n      setUploadedFile(prev => prev ? { \n        ...prev, \n        status: 'error', \n        error: err instanceof Error ? err.message : 'Upload failed' \n      } : null);\n    }\n  };\n\n  const handleFileRemove = () => {\n    setUploadedFile(null);\n    setCurrentStep('upload');\n  };\n\n  const handleConfigurationSubmit = async (config: GenerationConfig) => {\n    try {\n      // Create session first\n      const newSession = await createSession(config.domain, config.user_context);\n\n      if (newSession && uploadedFile) {\n        // Upload file to session using the returned session object\n        const uploadResponse = await apiService.uploadFile(newSession.session_id, uploadedFile.file);\n\n        if (!uploadResponse.success) {\n          throw new Error(uploadResponse.error || 'File upload failed');\n        }\n\n        // Start generation using the returned session object\n        const generationResponse = await apiService.startGeneration(\n          newSession.session_id,\n          config.n_rows,\n          config.batch_size\n        );\n\n        if (!generationResponse.success) {\n          throw new Error(generationResponse.error || 'Generation failed to start');\n        }\n\n        setCurrentStep('processing');\n      }\n    } catch (err) {\n      console.error('Failed to start generation:', err);\n      // Show error to user\n      alert(`Failed to start generation: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    }\n  };\n\n  const getTotalTokens = () => {\n    // Use actual token usage from backend if available, otherwise fall back to agent tokens\n    if (tokenUsage) {\n      return tokenUsage.total_tokens || 0;\n    }\n    return agents.reduce((total, agent) => total + agent.tokens_used, 0);\n  };\n\n  // Fetch token usage when session is completed\n  useEffect(() => {\n    if (session?.status === 'completed' && !tokenUsage) {\n      fetchTokenUsage();\n    }\n  }, [session?.status, tokenUsage, fetchTokenUsage]);\n\n  // Update current step when session completes\n  useEffect(() => {\n    if (session?.status === 'completed') {\n      setCurrentStep('results');\n    }\n  }, [session?.status]);\n\n  const handleStartNewSession = () => {\n    // Reset to upload step\n    setCurrentStep('upload');\n    // Clear any existing session data\n    cleanup();\n  };\n\n  const isProcessing = currentStep === 'processing';\n  const hasActiveAgents = agents.some(agent => agent.status === 'running');\n\n  return (\n    <div className=\"min-h-screen gradient-bg\">\n      <Header \n        totalTokens={getTotalTokens()} \n        isAnimating={hasActiveAgents}\n      />\n\n      <main className=\"container mx-auto px-6 py-8\">\n        <div className=\"max-w-6xl mx-auto space-y-8\">\n          {/* Hero Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"text-center space-y-4\"\n          >\n            <motion.h1\n              className=\"text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\"\n              animate={{\n                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']\n              }}\n              transition={{\n                duration: 8,\n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n              style={{ backgroundSize: '200% 200%' }}\n            >\n              Generate Realistic Synthetic Data\n            </motion.h1>\n            <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\n              Upload your CSV data and let our AI agents create high-quality synthetic datasets for testing and development\n            </p>\n          </motion.div>\n\n          {/* Main Content */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Left Column - Upload & Configuration OR Results */}\n            <div className=\"lg:col-span-2 space-y-6\">\n              {currentStep === 'results' ? (\n                  /* Results Display */\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5 }}\n                  >\n                    {session?.session_id && (\n                      <ResultsDisplay\n                        sessionId={session.session_id}\n                        onStartNewSession={handleStartNewSession}\n                      />\n                    )}\n                  </motion.div>\n                ) : (\n                  <>\n                    {/* File Upload */}\n                  <motion.div\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.5, delay: 0.2 }}\n                  >\n                    <FileUpload\n                      onFileSelect={handleFileSelect}\n                      onFileRemove={handleFileRemove}\n                      uploadedFile={uploadedFile}\n                      isUploading={uploadedFile?.status === 'uploading'}\n                      error={uploadedFile?.error}\n                    />\n                  </motion.div>\n\n                  {/* Configuration Form */}\n                  {currentStep !== 'upload' && (\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ duration: 0.5 }}\n                    >\n                      <ConfigurationForm\n                        onSubmit={handleConfigurationSubmit}\n                        isLoading={loading}\n                        disabled={isProcessing}\n                      />\n                    </motion.div>\n                  )}\n                </>\n              )}\n            </div>\n\n            {/* Right Column - Agent Status */}\n            <div className=\"space-y-4 relative\">\n              {/* Floating particles when processing */}\n              {isProcessing && <FloatingParticles count={15} />}\n\n              <motion.div\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.5, delay: 0.3 }}\n              >\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h2 className=\"text-2xl font-semibold\">AI Agents</h2>\n                  {isProcessing && session?.status !== 'completed' && (\n                    <motion.div\n                      initial={{ opacity: 0, scale: 0.8 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      className=\"flex items-center space-x-2\"\n                    >\n                      <PulsingDots size=\"sm\" color=\"blue\" />\n                      <span className=\"text-sm text-muted-foreground\">Processing...</span>\n                    </motion.div>\n                  )}\n                </div>\n\n                <div className=\"space-y-4\">\n                  {agents.map((agent, index) => (\n                    <AgentStatusCard\n                      key={agent.name}\n                      agent={agent}\n                      index={index}\n                    />\n                  ))}\n                </div>\n\n                {/* Processing Status Message */}\n                {isProcessing && session?.message && session?.status !== 'completed' && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\"\n                  >\n                    <div className=\"flex items-center space-x-2\">\n                      <TypingIndicator />\n                      <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                        {session.message}\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n              </motion.div>\n            </div>\n          </div>\n\n\n\n          {/* Error Display */}\n          {error && (\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"rounded-lg bg-red-50 border border-red-200 p-4 dark:bg-red-900/20 dark:border-red-800\"\n            >\n              <p className=\"text-red-600 dark:text-red-400\">{error}</p>\n            </motion.div>\n          )}\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,EACJ,OAAO,EACP,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,aAAa,EACb,UAAU,EACV,eAAe,EACf,eAAe,EAChB,GAAG,CAAA,GAAA,0HAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqD;IAElG,MAAM,mBAAmB,OAAO;QAC9B,MAAM,aAA6B;YACjC;YACA,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;YACf,UAAU;YACV,QAAQ;QACV;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B,gBAAgB,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAa,UAAU;gBAAE,IAAI;YAE/E,4BAA4B;YAC5B,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;gBACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,gBAAgB,CAAA,OAAQ,OAAO;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAE,IAAI;YAC5D;YAEA,gBAAgB,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,QAAQ;oBAAa,UAAU;gBAAI,IAAI;YACjF,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,gBAAgB,CAAA,OAAQ,OAAO;oBAC7B,GAAG,IAAI;oBACP,QAAQ;oBACR,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC9C,IAAI;QACN;IACF;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,eAAe;IACjB;IAEA,MAAM,4BAA4B,OAAO;QACvC,IAAI;YACF,uBAAuB;YACvB,MAAM,aAAa,MAAM,cAAc,OAAO,MAAM,EAAE,OAAO,YAAY;YAEzE,IAAI,cAAc,cAAc;gBAC9B,2DAA2D;gBAC3D,MAAM,iBAAiB,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU,CAAC,WAAW,UAAU,EAAE,aAAa,IAAI;gBAE3F,IAAI,CAAC,eAAe,OAAO,EAAE;oBAC3B,MAAM,IAAI,MAAM,eAAe,KAAK,IAAI;gBAC1C;gBAEA,qDAAqD;gBACrD,MAAM,qBAAqB,MAAM,sHAAA,CAAA,aAAU,CAAC,eAAe,CACzD,WAAW,UAAU,EACrB,OAAO,MAAM,EACb,OAAO,UAAU;gBAGnB,IAAI,CAAC,mBAAmB,OAAO,EAAE;oBAC/B,MAAM,IAAI,MAAM,mBAAmB,KAAK,IAAI;gBAC9C;gBAEA,eAAe;YACjB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,qBAAqB;YACrB,MAAM,CAAC,4BAA4B,EAAE,eAAe,QAAQ,IAAI,OAAO,GAAG,iBAAiB;QAC7F;IACF;IAEA,MAAM,iBAAiB;QACrB,wFAAwF;QACxF,IAAI,YAAY;YACd,OAAO,WAAW,YAAY,IAAI;QACpC;QACA,OAAO,OAAO,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,WAAW,EAAE;IACpE;IAEA,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,WAAW,eAAe,CAAC,YAAY;YAClD;QACF;IACF,GAAG;QAAC,SAAS;QAAQ;QAAY;KAAgB;IAEjD,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,WAAW,aAAa;YACnC,eAAe;QACjB;IACF,GAAG;QAAC,SAAS;KAAO;IAEpB,MAAM,wBAAwB;QAC5B,uBAAuB;QACvB,eAAe;QACf,kCAAkC;QAClC;IACF;IAEA,MAAM,eAAe,gBAAgB;IACrC,MAAM,kBAAkB,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAE9D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,SAAM;gBACL,aAAa;gBACb,aAAa;;;;;;0BAGf,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCACP,oBAAoB;4CAAC;4CAAU;4CAAY;yCAAS;oCACtD;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;oCACA,OAAO;wCAAE,gBAAgB;oCAAY;8CACtC;;;;;;8CAGD,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;sCAMjE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,YACb,mBAAmB,iBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;kDAE3B,SAAS,4BACR,8OAAC,gJAAA,CAAA,iBAAc;4CACb,WAAW,QAAQ,UAAU;4CAC7B,mBAAmB;;;;;;;;;;6DAKzB;;0DAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;0DAExC,cAAA,8OAAC,4IAAA,CAAA,aAAU;oDACT,cAAc;oDACd,cAAc;oDACd,cAAc;oDACd,aAAa,cAAc,WAAW;oDACtC,OAAO,cAAc;;;;;;;;;;;4CAKxB,gBAAgB,0BACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC,mJAAA,CAAA,oBAAiB;oDAChB,UAAU;oDACV,WAAW;oDACX,UAAU;;;;;;;;;;;;;;;;;;8CAStB,8OAAC;oCAAI,WAAU;;wCAEZ,8BAAgB,8OAAC,iJAAA,CAAA,oBAAiB;4CAAC,OAAO;;;;;;sDAE3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;wDACtC,gBAAgB,SAAS,WAAW,6BACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;gEAAG,OAAO;4DAAI;4DAClC,SAAS;gEAAE,SAAS;gEAAG,OAAO;4DAAE;4DAChC,WAAU;;8EAEV,8OAAC,iJAAA,CAAA,cAAW;oEAAC,MAAK;oEAAK,OAAM;;;;;;8EAC7B,8OAAC;oEAAK,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAKtD,8OAAC;oDAAI,WAAU;8DACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,iJAAA,CAAA,kBAAe;4DAEd,OAAO;4DACP,OAAO;2DAFF,MAAM,IAAI;;;;;;;;;;gDAQpB,gBAAgB,SAAS,WAAW,SAAS,WAAW,6BACvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iJAAA,CAAA,kBAAe;;;;;0EAChB,8OAAC;gEAAE,WAAU;0EACV,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAY7B,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7D", "debugId": null}}]}