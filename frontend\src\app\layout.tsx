import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "DataEcho - AI Synthetic Data Platform",
  description: "Generate realistic synthetic data using AI agents for testing and development",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.variable} font-sans antialiased bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen`}
      >
        {children}
      </body>
    </html>
  );
}
