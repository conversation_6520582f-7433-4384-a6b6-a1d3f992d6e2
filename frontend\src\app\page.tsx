'use client';

import { useState, useEffect } from 'react';
import { motion } from 'motion/react';
import { Header } from '@/components/features/Header';
import { FileUpload } from '@/components/features/FileUpload';
import { AgentStatusCard } from '@/components/features/AgentStatusCard';
import { ConfigurationForm } from '@/components/features/ConfigurationForm';
import { ResultsDisplay } from '@/components/features/ResultsDisplay';
import { PulsingDots, TypingIndicator, FloatingParticles } from '@/components/ui/loading-animations';
import { useSession } from '@/hooks/useSession';
import { apiService } from '@/services/api';
import type { FileUpload as FileUploadType, GenerationConfig } from '@/types';

export default function Home() {
  const {
    session,
    agents,
    tokenUsage,
    loading,
    error,
    createSession,
    uploadFile,
    startGeneration,
    fetchTokenUsage
  } = useSession();

  const [uploadedFile, setUploadedFile] = useState<FileUploadType | null>(null);
  const [currentStep, setCurrentStep] = useState<'upload' | 'configure' | 'processing' | 'results'>('upload');

  const handleFileSelect = async (file: File) => {
    const fileUpload: FileUploadType = {
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: 'pending'
    };

    setUploadedFile(fileUpload);

    try {
      // Simulate upload progress
      setUploadedFile(prev => prev ? { ...prev, status: 'uploading', progress: 0 } : null);
      
      // Simulate progress updates
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setUploadedFile(prev => prev ? { ...prev, progress: i } : null);
      }

      setUploadedFile(prev => prev ? { ...prev, status: 'completed', progress: 100 } : null);
      setCurrentStep('configure');
    } catch (err) {
      setUploadedFile(prev => prev ? { 
        ...prev, 
        status: 'error', 
        error: err instanceof Error ? err.message : 'Upload failed' 
      } : null);
    }
  };

  const handleFileRemove = () => {
    setUploadedFile(null);
    setCurrentStep('upload');
  };

  const handleConfigurationSubmit = async (config: GenerationConfig) => {
    try {
      // Create session first
      const newSession = await createSession(config.domain, config.user_context);

      if (newSession && uploadedFile) {
        // Upload file to session using the returned session object
        const uploadResponse = await apiService.uploadFile(newSession.session_id, uploadedFile.file);

        if (!uploadResponse.success) {
          throw new Error(uploadResponse.error || 'File upload failed');
        }

        // Start generation using the returned session object
        const generationResponse = await apiService.startGeneration(
          newSession.session_id,
          config.n_rows,
          config.batch_size
        );

        if (!generationResponse.success) {
          throw new Error(generationResponse.error || 'Generation failed to start');
        }

        setCurrentStep('processing');
      }
    } catch (err) {
      console.error('Failed to start generation:', err);
      // Show error to user
      alert(`Failed to start generation: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const getTotalTokens = () => {
    // Use actual token usage from backend if available, otherwise fall back to agent tokens
    if (tokenUsage) {
      return tokenUsage.total_tokens || 0;
    }
    return agents.reduce((total, agent) => total + agent.tokens_used, 0);
  };

  // Fetch token usage when session is completed
  useEffect(() => {
    if (session?.status === 'completed' && !tokenUsage) {
      fetchTokenUsage();
    }
  }, [session?.status, tokenUsage, fetchTokenUsage]);

  // Update current step when session completes
  useEffect(() => {
    if (session?.status === 'completed') {
      setCurrentStep('results');
    }
  }, [session?.status]);

  const handleStartNewSession = () => {
    // Reset to upload step
    setCurrentStep('upload');
    // Clear any existing session data
    cleanup();
  };

  const isProcessing = currentStep === 'processing';
  const hasActiveAgents = agents.some(agent => agent.status === 'running');

  return (
    <div className="min-h-screen gradient-bg">
      <Header 
        totalTokens={getTotalTokens()} 
        isAnimating={hasActiveAgents}
      />

      <main className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-4"
          >
            <motion.h1
              className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "linear"
              }}
              style={{ backgroundSize: '200% 200%' }}
            >
              Generate Realistic Synthetic Data
            </motion.h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Upload your CSV data and let our AI agents create high-quality synthetic datasets for testing and development
            </p>
          </motion.div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Upload & Configuration OR Results */}
            <div className="lg:col-span-2 space-y-6">
              {currentStep === 'results' ? (
                  /* Results Display */
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    {session?.session_id && (
                      <ResultsDisplay
                        sessionId={session.session_id}
                        onStartNewSession={handleStartNewSession}
                      />
                    )}
                  </motion.div>
                ) : (
                  <>
                    {/* File Upload */}
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <FileUpload
                      onFileSelect={handleFileSelect}
                      onFileRemove={handleFileRemove}
                      uploadedFile={uploadedFile}
                      isUploading={uploadedFile?.status === 'uploading'}
                      error={uploadedFile?.error}
                    />
                  </motion.div>

                  {/* Configuration Form */}
                  {currentStep !== 'upload' && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <ConfigurationForm
                        onSubmit={handleConfigurationSubmit}
                        isLoading={loading}
                        disabled={isProcessing}
                      />
                    </motion.div>
                  )}
                </>
              )}
            </div>

            {/* Right Column - Agent Status */}
            <div className="space-y-4 relative">
              {/* Floating particles when processing */}
              {isProcessing && <FloatingParticles count={15} />}

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-2xl font-semibold">AI Agents</h2>
                  {isProcessing && session?.status !== 'completed' && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="flex items-center space-x-2"
                    >
                      <PulsingDots size="sm" color="blue" />
                      <span className="text-sm text-muted-foreground">Processing...</span>
                    </motion.div>
                  )}
                </div>

                <div className="space-y-4">
                  {agents.map((agent, index) => (
                    <AgentStatusCard
                      key={agent.name}
                      agent={agent}
                      index={index}
                    />
                  ))}
                </div>

                {/* Processing Status Message */}
                {isProcessing && session?.message && session?.status !== 'completed' && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                  >
                    <div className="flex items-center space-x-2">
                      <TypingIndicator />
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        {session.message}
                      </p>
                    </div>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </div>



          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="rounded-lg bg-red-50 border border-red-200 p-4 dark:bg-red-900/20 dark:border-red-800"
            >
              <p className="text-red-600 dark:text-red-400">{error}</p>
            </motion.div>
          )}
        </div>
      </main>
    </div>
  );
}
