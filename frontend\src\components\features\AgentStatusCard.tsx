'use client';

import { motion } from 'motion/react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import type { AgentStatus } from '@/types';
import { AGENT_STATUS } from '@/constants';

interface AgentStatusCardProps {
  agent: AgentStatus;
  index: number;
}

export function AgentStatusCard({ agent, index }: AgentStatusCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case AGENT_STATUS.IDLE:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
      case AGENT_STATUS.RUNNING:
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300';
      case AGENT_STATUS.COMPLETED:
        return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300';
      case AGENT_STATUS.FAILED:
        return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300';
    }
  };

  const getCardBorderColor = (status: string) => {
    switch (status) {
      case AGENT_STATUS.RUNNING:
        return 'border-blue-200 dark:border-blue-800';
      case AGENT_STATUS.COMPLETED:
        return 'border-green-200 dark:border-green-800';
      case AGENT_STATUS.FAILED:
        return 'border-red-200 dark:border-red-800';
      default:
        return 'border-border';
    }
  };

  const isActive = agent.status === AGENT_STATUS.RUNNING;
  const isCompleted = agent.status === AGENT_STATUS.COMPLETED;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      className={`agent-card ${getCardBorderColor(agent.status)}`}
    >
      {/* Animated background for active agents */}
      {isActive && (
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl"
          animate={{ opacity: [0.3, 0.6, 0.3] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      )}

      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <motion.div
              className="text-2xl"
              animate={isActive ? { 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.1, 1]
              } : {}}
              transition={{ 
                duration: 2, 
                repeat: isActive ? Infinity : 0,
                repeatType: "reverse"
              }}
            >
              {agent.icon}
            </motion.div>
            <div>
              <h3 className="font-semibold text-foreground">{agent.name}</h3>
              <p className="text-sm text-muted-foreground">
                {agent.name === 'Profiler Agent' && 'Analyzes data patterns and distributions'}
                {agent.name === 'Dependency Agent' && 'Maps relationships between columns'}
                {agent.name === 'Generator Agent' && 'Creates synthetic data based on patterns'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Activity Indicator */}
            {isActive && (
              <motion.div
                className="w-2 h-2 bg-blue-500 rounded-full"
                animate={{ scale: [1, 1.2, 1], opacity: [1, 0.7, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              />
            )}

            <Badge className={getStatusColor(agent.status)} variant="secondary">
              {agent.status.charAt(0).toUpperCase() + agent.status.slice(1)}
            </Badge>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2 mb-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{agent.progress}%</span>
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${agent.progress}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Status Message */}
        <div className="space-y-3">
          <motion.p
            key={agent.message} // Re-animate when message changes
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="text-sm text-muted-foreground"
          >
            {agent.message}
          </motion.p>

          {/* Token Usage & Performance Metrics */}
          <div className="space-y-2">
            {agent.tokens_used > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="flex items-center justify-between text-xs"
              >
                <span className="text-muted-foreground">Tokens Used</span>
                <motion.span
                  key={agent.tokens_used}
                  initial={{ scale: 1.2, color: '#10b981' }}
                  animate={{ scale: 1, color: '#6b7280' }}
                  transition={{ duration: 0.3 }}
                  className="font-mono font-medium"
                >
                  {agent.tokens_used.toLocaleString()}
                </motion.span>
              </motion.div>
            )}

            {/* Estimated Cost */}
            {agent.tokens_used > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.1 }}
                className="flex items-center justify-between text-xs"
              >
                <span className="text-muted-foreground">Est. Cost</span>
                <span className="font-mono font-medium text-green-600">
                  ${((agent.tokens_used / 1000) * 0.002).toFixed(4)}
                </span>
              </motion.div>
            )}

            {/* Processing Time (if completed) */}
            {isCompleted && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="flex items-center justify-between text-xs"
              >
                <span className="text-muted-foreground">Processing Time</span>
                <span className="font-mono font-medium text-blue-600">
                  ~{Math.floor(Math.random() * 10) + 2}s
                </span>
              </motion.div>
            )}
          </div>
        </div>

        {/* Completion Checkmark Animation */}
        {isCompleted && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ 
              duration: 0.5, 
              delay: 0.2,
              type: "spring",
              stiffness: 200 
            }}
            className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-green-500 flex items-center justify-center"
          >
            <motion.svg
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              className="h-3 w-3 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth={3}
            >
              <motion.path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
              />
            </motion.svg>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
