'use client';

import { useState } from 'react';
import { motion } from 'motion/react';
import { Setting<PERSON>, Play, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DOMAIN_SUGGESTIONS, BATCH_SIZE_OPTIONS } from '@/constants';
import type { GenerationConfig } from '@/types';

interface ConfigurationFormProps {
  onSubmit: (config: GenerationConfig) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

export function ConfigurationForm({ onSubmit, isLoading = false, disabled = false }: ConfigurationFormProps) {
  const [config, setConfig] = useState<GenerationConfig>({
    domain: '',
    user_context: '',
    n_rows: 1000,
    batch_size: 500
  });

  const [errors, setErrors] = useState<Partial<GenerationConfig>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<GenerationConfig> = {};

    if (!config.domain.trim()) {
      newErrors.domain = 'Domain is required';
    }

    if (!config.user_context.trim()) {
      newErrors.user_context = 'Context description is required';
    }

    if (config.n_rows < 10 || config.n_rows > 100000) {
      newErrors.n_rows = 10; // Using number to indicate min value
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(config);
    }
  };

  const handleDomainSelect = (domain: string) => {
    setConfig(prev => ({ ...prev, domain }));
    if (errors.domain) {
      setErrors(prev => ({ ...prev, domain: undefined }));
    }
  };

  const handleBatchSizeSelect = (batchSize: string) => {
    setConfig(prev => ({ ...prev, batch_size: parseInt(batchSize) }));
  };

  const isFormValid = config.domain && config.user_context && config.n_rows >= 10;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <Card className="card-gradient">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-primary" />
            <CardTitle>Generation Configuration</CardTitle>
          </div>
          <CardDescription>
            Configure the parameters for synthetic data generation
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Domain Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Domain <span className="text-red-500">*</span>
              </label>
              <Select 
                value={config.domain} 
                onValueChange={handleDomainSelect}
                disabled={disabled}
              >
                <SelectTrigger className={errors.domain ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select your data domain" />
                </SelectTrigger>
                <SelectContent>
                  {DOMAIN_SUGGESTIONS.map((domain) => (
                    <SelectItem key={domain} value={domain}>
                      {domain}
                    </SelectItem>
                  ))}
                  <SelectItem value="custom">Custom Domain</SelectItem>
                </SelectContent>
              </Select>
              {errors.domain && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-red-500"
                >
                  {errors.domain}
                </motion.p>
              )}
            </div>

            {/* Custom Domain Input */}
            {config.domain === 'custom' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                <label className="text-sm font-medium">Custom Domain</label>
                <Input
                  placeholder="Enter your custom domain"
                  value={config.domain === 'custom' ? '' : config.domain}
                  onChange={(e) => setConfig(prev => ({ ...prev, domain: e.target.value }))}
                  disabled={disabled}
                />
              </motion.div>
            )}

            {/* User Context */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Context Description <span className="text-red-500">*</span>
              </label>
              <Textarea
                placeholder="Describe your data context, use case, and any specific requirements..."
                value={config.user_context}
                onChange={(e) => {
                  setConfig(prev => ({ ...prev, user_context: e.target.value }));
                  if (errors.user_context) {
                    setErrors(prev => ({ ...prev, user_context: undefined }));
                  }
                }}
                className={`min-h-[100px] ${errors.user_context ? 'border-red-500' : ''}`}
                disabled={disabled}
              />
              {errors.user_context && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-sm text-red-500"
                >
                  {errors.user_context}
                </motion.p>
              )}
              <p className="text-xs text-muted-foreground">
                Provide context about your data to help our AI agents generate more accurate synthetic data
              </p>
            </div>

            {/* Generation Parameters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Number of Rows</label>
                <Input
                  type="number"
                  min="10"
                  max="100000"
                  value={config.n_rows}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    setConfig(prev => ({ ...prev, n_rows: value }));
                    if (errors.n_rows) {
                      setErrors(prev => ({ ...prev, n_rows: undefined }));
                    }
                  }}
                  className={errors.n_rows ? 'border-red-500' : ''}
                  disabled={disabled}
                />
                {errors.n_rows && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-sm text-red-500"
                  >
                    Must be between 10 and 100,000 rows
                  </motion.p>
                )}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Batch Size</label>
                <Select 
                  value={config.batch_size.toString()} 
                  onValueChange={handleBatchSizeSelect}
                  disabled={disabled}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {BATCH_SIZE_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value.toString()}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Larger batches are faster but use more memory
                </p>
              </div>
            </div>

            {/* Submit Button */}
            <motion.div
              className="pt-4"
              whileHover={{ scale: isFormValid && !disabled ? 1.02 : 1 }}
              whileTap={{ scale: isFormValid && !disabled ? 0.98 : 1 }}
            >
              <Button
                type="submit"
                className="w-full"
                disabled={!isFormValid || disabled || isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting Generation...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Start Data Generation
                  </>
                )}
              </Button>
            </motion.div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
