'use client';

import { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Upload, File, CheckCircle, X, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { FILE_CONSTRAINTS } from '@/constants';
import type { FileUpload } from '@/types';

interface FileUploadProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  uploadedFile?: FileUpload | null;
  isUploading?: boolean;
  error?: string | null;
}

export function FileUpload({ 
  onFileSelect, 
  onFileRemove, 
  uploadedFile, 
  isUploading = false,
  error 
}: FileUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = useCallback((file: File): string | null => {
    if (file.size > FILE_CONSTRAINTS.MAX_SIZE) {
      return `File size must be less than ${FILE_CONSTRAINTS.MAX_SIZE / (1024 * 1024)}MB`;
    }
    
    if (!FILE_CONSTRAINTS.ALLOWED_TYPES.some(type => file.name.toLowerCase().endsWith(type))) {
      return 'Only CSV files are allowed';
    }
    
    return null;
  }, []);

  const handleFileSelect = useCallback((file: File) => {
    const validationError = validateFile(file);
    if (validationError) {
      // Handle validation error - could show toast or set error state
      console.error('File validation failed:', validationError);
      return;
    }
    
    onFileSelect(file);
  }, [validateFile, onFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleBrowseClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Upload Your CSV Data</h3>
        <p className="text-sm text-muted-foreground">
          Upload your CSV file and let our AI agents create realistic synthetic data
        </p>
      </div>

      <AnimatePresence mode="wait">
        {!uploadedFile ? (
          <motion.div
            key="upload-zone"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={`upload-zone ${isDragOver ? 'dragover' : ''}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <motion.div
              className="flex flex-col items-center gap-4"
              animate={isDragOver ? { scale: 1.05 } : { scale: 1 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="flex h-16 w-16 items-center justify-center rounded-full bg-primary/10"
                animate={isDragOver ? { rotate: 5 } : { rotate: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Upload className="h-8 w-8 text-primary" />
              </motion.div>
              
              <div className="text-center">
                <p className="text-lg font-medium">
                  {isDragOver ? 'Drop your CSV file here' : 'Drop your CSV file here'}
                </p>
                <p className="text-sm text-muted-foreground">
                  or click to browse and select your data file
                </p>
              </div>
              
              <Button onClick={handleBrowseClick} variant="outline">
                Select CSV File
              </Button>
              
              <p className="text-xs text-muted-foreground">
                Supports CSV files up to {FILE_CONSTRAINTS.MAX_SIZE / (1024 * 1024)}MB
              </p>
            </motion.div>

            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileInputChange}
              className="hidden"
            />
          </motion.div>
        ) : (
          <motion.div
            key="uploaded-file"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="rounded-xl border bg-card p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                  {uploadedFile.status === 'completed' ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : uploadedFile.status === 'error' ? (
                    <AlertCircle className="h-5 w-5 text-red-600" />
                  ) : (
                    <File className="h-5 w-5 text-blue-600" />
                  )}
                </div>
                
                <div>
                  <p className="font-medium">{uploadedFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(uploadedFile.size)} • Ready for processing
                  </p>
                </div>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onFileRemove}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {isUploading && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-3 space-y-2"
              >
                <Progress value={uploadedFile.progress} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  Uploading... {uploadedFile.progress}%
                </p>
              </motion.div>
            )}

            {error && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="mt-3 rounded-md bg-red-50 p-3 dark:bg-red-900/20"
              >
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
