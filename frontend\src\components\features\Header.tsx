'use client';

import { motion } from 'motion/react';
import { Zap } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface HeaderProps {
  totalTokens?: number;
  isAnimating?: boolean;
}

export function Header({ totalTokens = 0, isAnimating = false }: HeaderProps) {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/80 backdrop-blur-md">
      <div className="container mx-auto flex h-16 items-center justify-between px-6">
        {/* Logo and Title */}
        <motion.div 
          className="flex items-center gap-3"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-lg">
            DE
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground">DataEcho</h1>
            <p className="text-xs text-muted-foreground">AI Synthetic Data Platform</p>
          </div>
        </motion.div>

        {/* Token Counter */}
        <motion.div
          className="token-counter"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            animate={isAnimating ? { scale: [1, 1.1, 1] } : {}}
            transition={{ duration: 0.3 }}
          >
            <Zap className="h-4 w-4 text-green-500" />
          </motion.div>
          
          <motion.span
            className="font-mono text-sm font-medium"
            key={totalTokens} // This will trigger re-animation when tokens change
            initial={{ scale: 1.2, color: '#10b981' }}
            animate={{ scale: 1, color: '#374151' }}
            transition={{ duration: 0.3 }}
          >
            {totalTokens.toLocaleString()}
          </motion.span>
          
          <Badge variant="secondary" className="text-xs">
            tokens
          </Badge>
        </motion.div>
      </div>
    </header>
  );
}
