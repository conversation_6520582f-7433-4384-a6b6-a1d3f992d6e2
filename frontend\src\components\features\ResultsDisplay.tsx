'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'motion/react';
import { Download, Eye, BarChart3, CheckCircle, AlertCircle, Zap, DollarSign, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import { apiService } from '@/services/api';
import type { Session, TokenUsage } from '@/types';

interface ResultsDisplayProps {
  sessionId: string;
  session: Session;
  tokenUsage: TokenUsage | null;
  onStartNewSession?: () => void;
}

export function ResultsDisplay({ sessionId, session, tokenUsage, onStartNewSession }: ResultsDisplayProps) {
  const [activeTab, setActiveTab] = useState('preview');

  // Extract data directly from session prop (no additional API calls needed)
  const rawPreviewData = session?.preview_data || null;
  const qualityMetrics = session?.quality_metrics || null;

  // Transform preview data to expected format
  const previewData = rawPreviewData ? {
    data: rawPreviewData,
    columns: rawPreviewData.length > 0 ? Object.keys(rawPreviewData[0]) : [],
    preview_rows: rawPreviewData.length,
    total_rows: qualityMetrics?.total_rows_generated || rawPreviewData.length
  } : null;

  const handleDownload = async () => {
    try {
      const blob = await apiService.downloadResults(sessionId);
      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `dataecho_results_${sessionId}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  console.log('🎯 ResultsDisplay render check:', {
    hasSession: !!session,
    sessionStatus: session?.status,
    sessionId: session?.session_id,
    propSessionId: sessionId,
    shouldRender: true
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-6 w-6 text-green-500" />
          <h2 className="text-2xl font-semibold">Generation Complete!</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={handleDownload} className="flex items-center space-x-2">
            <Download className="h-4 w-4" />
            <span>Download CSV</span>
          </Button>
          {onStartNewSession && (
            <Button
              onClick={onStartNewSession}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>New Session</span>
            </Button>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      {qualityMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Rows</p>
                  <p className="text-2xl font-bold">{qualityMetrics.total_rows}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Quality Score</p>
                  <p className="text-2xl font-bold">{Math.round(qualityMetrics.overall_quality * 100)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Eye className="h-4 w-4 text-purple-500" />
                <div>
                  <p className="text-sm font-medium">Columns</p>
                  <p className="text-2xl font-bold">{qualityMetrics.columns_count}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">Completion</p>
                  <p className="text-2xl font-bold">{Math.round(qualityMetrics.generation_performance?.completion_rate || 100)}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Detailed Results */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Data</CardTitle>
          <CardDescription>
            Preview and analyze your synthetic data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="preview">Data Preview</TabsTrigger>
              <TabsTrigger value="quality">Quality Metrics</TabsTrigger>
              <TabsTrigger value="tokens">Token Usage</TabsTrigger>
              <TabsTrigger value="insights">AI Insights</TabsTrigger>
            </TabsList>
            
            <TabsContent value="preview" className="space-y-4">
              {previewData ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">
                      Showing {previewData.preview_rows} of {previewData.total_rows} rows
                    </p>
                    <Badge variant="outline">
                      {previewData.columns?.length} columns
                    </Badge>
                  </div>
                  
                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          {previewData.columns?.map((column: string) => (
                            <TableHead key={column}>{column}</TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {previewData.data?.map((row: any, index: number) => (
                          <TableRow key={index}>
                            {previewData.columns?.map((column: string) => (
                              <TableCell key={column}>
                                {row[column]?.toString() || '-'}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No preview data available
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="quality" className="space-y-4">
              {qualityMetrics && (
                <div className="space-y-6">
                  {/* Overall Quality - using completion_rate as overall quality */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Overall Quality</h4>
                      <span className="text-sm font-medium">{Math.round(qualityMetrics.completion_rate || 0)}%</span>
                    </div>
                    <Progress value={qualityMetrics.completion_rate || 0} className="h-2" />
                  </div>
                  
                  {/* Data Consistency */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Data Consistency</h4>
                      <span className="text-sm font-medium">{Math.round(qualityMetrics.data_consistency * 100)}%</span>
                    </div>
                    <Progress value={qualityMetrics.data_consistency * 100} className="h-2" />
                  </div>
                  
                  {/* Generation Performance */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Generation Time</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-lg font-semibold">{qualityMetrics.generation_time || 'N/A'}</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Rows Generated</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-lg font-semibold">{qualityMetrics.total_rows_generated || 0}</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="tokens" className="space-y-4">
              {tokenUsage ? (
                <div className="space-y-4">
                  {/* Total Usage Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5 text-green-500" />
                        Total Token Usage
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <p className="text-2xl font-bold text-green-600">{(tokenUsage.total_tokens || 0).toLocaleString()}</p>
                          <p className="text-sm text-muted-foreground">Total Tokens</p>
                        </div>
                        <div className="text-center">
                          <p className="text-2xl font-bold text-blue-600">${(tokenUsage.total_cost || 0).toFixed(4)}</p>
                          <p className="text-sm text-muted-foreground">Estimated Cost</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Agent-Level Breakdown */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-blue-500" />
                        Agent Breakdown
                      </CardTitle>
                      <CardDescription>Token usage by individual AI agents</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Profiler Agent */}
                        <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">📊</div>
                            <div>
                              <p className="font-medium">Profiler Agent</p>
                              <p className="text-sm text-muted-foreground">Data analysis & pattern detection</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{(tokenUsage.agents?.ProfilerAgent?.tokens_used || tokenUsage.agents?.['Profiler Agent']?.tokens_used || 0).toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">tokens</p>
                          </div>
                        </div>

                        {/* Dependency Agent */}
                        <div className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">🔗</div>
                            <div>
                              <p className="font-medium">Dependency Agent</p>
                              <p className="text-sm text-muted-foreground">Relationship mapping</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{(tokenUsage.agents?.DependencyAgent?.tokens_used || tokenUsage.agents?.['Dependency Agent']?.tokens_used || 0).toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">tokens</p>
                          </div>
                        </div>

                        {/* Generator Agent */}
                        <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">⚡</div>
                            <div>
                              <p className="font-medium">Generator Agent</p>
                              <p className="text-sm text-muted-foreground">Synthetic data generation</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{(tokenUsage.agents?.GeneratorAgent?.tokens_used || tokenUsage.agents?.['Generator Agent']?.tokens_used || 0).toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">tokens</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-muted-foreground">Token usage data not available</p>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              {session?.results?.profiler?.profile && (
                <div className="space-y-4">
                  {/* Dataset Overview */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-blue-500" />
                        Dataset Overview
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Rows</p>
                          <p className="text-lg font-semibold">{session.results.profiler.profile?.dataset_info?.row_count || 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Columns</p>
                          <p className="text-lg font-semibold">{session.results.profiler.profile?.dataset_info?.column_count || 'N/A'}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Column Analysis */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Eye className="h-5 w-5 text-green-500" />
                        Column Analysis
                      </CardTitle>
                      <CardDescription>Detailed analysis of each column in your dataset</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {session.results.profiler.profile.columns?.map((column: any, index: number) => (
                          <div key={index} className="border rounded-lg p-4 space-y-3">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium text-lg">{column.name}</h4>
                              <div className="flex gap-2">
                                <Badge variant="outline">{column.type}</Badge>
                                <Badge variant="secondary">{column.semantic}</Badge>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="font-medium text-muted-foreground mb-1">Constraints</p>
                                <ul className="list-disc list-inside space-y-1">
                                  {column.constraints?.map((constraint: string, i: number) => (
                                    <li key={i} className="text-xs">{constraint}</li>
                                  ))}
                                </ul>
                              </div>

                              <div>
                                <p className="font-medium text-muted-foreground mb-1">Example Values</p>
                                <div className="flex flex-wrap gap-1">
                                  {column.example_values?.slice(0, 4).map((value: any, i: number) => (
                                    <Badge key={i} variant="outline" className="text-xs">{String(value)}</Badge>
                                  ))}
                                </div>
                              </div>
                            </div>

                            <div className="text-xs text-muted-foreground">
                              <p><strong>Generation Strategy:</strong> {column.recommended_generation_strategy}</p>
                              <p><strong>Confidence:</strong> {Math.round((column.profiling_confidence || 0) * 100)}%</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Global Rules */}
                  {session.results.profiler.profile.global_rules && session.results.profiler.profile.global_rules.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <AlertCircle className="h-5 w-5 text-orange-500" />
                          Global Rules & Relationships
                        </CardTitle>
                        <CardDescription>Business rules and relationships detected in your data</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {session.results.profiler.profile.global_rules.map((rule: any, index: number) => (
                            <div key={index} className="border-l-4 border-orange-200 pl-4 py-2">
                              <p className="text-sm font-medium">{rule.rule}</p>
                              <p className="text-xs text-muted-foreground mt-1">{rule.justification}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </motion.div>
  );
}
