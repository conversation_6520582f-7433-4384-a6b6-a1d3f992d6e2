// DataEcho Frontend Constants

export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
export const WS_BASE_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';

export const AGENTS = {
  PROFILER: {
    name: 'Profiler Agent',
    description: 'Analyzes data patterns and distributions',
    icon: '📊',
    color: 'blue'
  },
  DEPENDENCY: {
    name: 'Dependency Agent', 
    description: 'Maps relationships between columns',
    icon: '🔗',
    color: 'purple'
  },
  GENERATOR: {
    name: 'Generator Agent',
    description: 'Creates synthetic data based on patterns',
    icon: '⚡',
    color: 'green'
  }
} as const;

export const DOMAIN_SUGGESTIONS = [
  'Financial Services',
  'Healthcare',
  'Retail & E-commerce',
  'Manufacturing',
  'Technology',
  'Education',
  'Real Estate',
  'Transportation',
  'Energy & Utilities',
  'Government'
] as const;

export const BATCH_SIZE_OPTIONS = [
  { label: 'Small (100 rows/batch)', value: 100 },
  { label: 'Medium (500 rows/batch)', value: 500 },
  { label: 'Large (1000 rows/batch)', value: 1000 },
  { label: 'Extra Large (2000 rows/batch)', value: 2000 }
] as const;

export const FILE_CONSTRAINTS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['.csv'],
  MIME_TYPES: ['text/csv', 'application/csv']
} as const;

export const ANIMATION_DURATIONS = {
  FAST: 0.2,
  NORMAL: 0.3,
  SLOW: 0.5
} as const;

export const WEBSOCKET_EVENTS = {
  STATUS_UPDATE: 'status_update',
  PROGRESS: 'progress',
  COMPLETION: 'completion',
  ERROR: 'error'
} as const;

export const SESSION_STATUS = {
  CREATED: 'created',
  RUNNING: 'running', 
  COMPLETED: 'completed',
  FAILED: 'failed'
} as const;

export const AGENT_STATUS = {
  IDLE: 'idle',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed'
} as const;
