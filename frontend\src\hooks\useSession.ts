// DataEcho Session Management Hook

import { useState, useEffect, useCallback } from 'react';
import { apiService } from '@/services/api';
import { webSocketService } from '@/services/websocket';
import type { Session, AgentStatus, TokenUsage } from '@/types';
import { AGENTS, AGENT_STATUS } from '@/constants';

export function useSession() {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [agents, setAgents] = useState<AgentStatus[]>([
    {
      name: AGENTS.PROFILER.name,
      status: AGENT_STATUS.IDLE,
      progress: 0,
      message: 'Ready to analyze data',
      tokens_used: 0,
      icon: AGENTS.PROFILER.icon,
      color: AGENTS.PROFILER.color
    },
    {
      name: AGENTS.DEPENDENCY.name,
      status: AGENT_STATUS.IDLE,
      progress: 0,
      message: 'Ready to map relationships',
      tokens_used: 0,
      icon: AGENTS.DEPENDENCY.icon,
      color: AGENTS.DEPENDENCY.color
    },
    {
      name: AGENTS.GENERATOR.name,
      status: AGENT_STATUS.IDLE,
      progress: 0,
      message: 'Ready to generate data',
      tokens_used: 0,
      icon: AGENTS.GENERATOR.icon,
      color: AGENTS.GENERATOR.color
    }
  ]);
  const [tokenUsage, setTokenUsage] = useState<TokenUsage | null>(null);

  const createSession = useCallback(async (domain: string, userContext: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiService.createSession(domain, userContext);
      
      if (response.success && response.data) {
        setSession(response.data);
        
        // Connect WebSocket
        await webSocketService.connect(response.data.session_id);
        
        // Set up WebSocket event handlers
        setupWebSocketHandlers(response.data.session_id);
        
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to create session');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const setupWebSocketHandlers = useCallback((sessionId: string) => {
    // Status updates
    webSocketService.onStatusUpdate((message) => {
      if (message.session_id === sessionId) {
        setSession(prev => prev ? {
          ...prev,
          status: message.data.status,
          current_step: message.data.current_step,
          progress: message.data.progress,
          message: message.data.message,
          data: { ...prev.data, ...message.data.data }
        } : null);

        // Update agent status based on current step
        updateAgentStatus(message.data.current_step, message.data.progress, message.data.message);
      }
    });

    // Progress updates
    webSocketService.onProgress((message) => {
      if (message.session_id === sessionId) {
        setSession(prev => prev ? {
          ...prev,
          progress: message.data.progress
        } : null);
      }
    });

    // Completion
    webSocketService.onCompletion(async (message) => {
      if (message.session_id === sessionId) {
        try {
          // Fetch enhanced results when generation completes
          const resultsResponse = await apiService.getResults(sessionId);

          setSession(prev => prev ? {
            ...prev,
            status: 'completed',
            progress: 100,
            message: 'Generation completed successfully',
            results: resultsResponse.success ? resultsResponse.data : null
          } : null);

          // Mark all agents as completed
          setAgents(prev => prev.map(agent => ({
            ...agent,
            status: AGENT_STATUS.COMPLETED,
            progress: 100,
            message: `${agent.name} completed successfully`
          })));

          // Fetch token usage
          try {
            const tokenResponse = await apiService.getTokenUsage(message.session_id);
            if (tokenResponse.success) {
              setTokenUsage(tokenResponse.data);

              // Update individual agent token usage
              setAgents(prev => prev.map(agent => {
                let tokens = 0;
                const agentData = tokenResponse.data.agents || {};

                // Map frontend agent names to backend agent names
                if (agent.name === 'Profiler Agent') {
                  tokens = agentData['ProfilerAgent']?.tokens_used || agentData['Profiler Agent']?.tokens_used || 0;
                } else if (agent.name === 'Dependency Agent') {
                  tokens = agentData['DependencyAgent']?.tokens_used || agentData['Dependency Agent']?.tokens_used || 0;
                } else if (agent.name === 'Generator Agent') {
                  tokens = agentData['GeneratorAgent']?.tokens_used || agentData['Generator Agent']?.tokens_used || 0;
                }

                return {
                  ...agent,
                  tokens_used: tokens
                };
              }));
            }
          } catch (error) {
            console.error('Failed to fetch token usage:', error);
          }
        } catch (error) {
          console.error('Failed to fetch results on completion:', error);
          setSession(prev => prev ? {
            ...prev,
            status: 'completed',
            progress: 100,
            message: 'Generation completed successfully'
          } : null);

          // Mark all agents as completed even if results fetch fails
          setAgents(prev => prev.map(agent => ({
            ...agent,
            status: AGENT_STATUS.COMPLETED,
            progress: 100,
            message: `${agent.name} completed successfully`
          })));

          // Fetch token usage even if results fetch fails
          try {
            const tokenResponse = await apiService.getTokenUsage(session.session_id);
            if (tokenResponse.success) {
              setTokenUsage(tokenResponse.data);
            }
          } catch (error) {
            console.error('Failed to fetch token usage:', error);
          }
        }
      }
    });

    // Errors
    webSocketService.onError((message) => {
      if (message.session_id === sessionId) {
        setError(message.data.error || 'An error occurred');
        setSession(prev => prev ? {
          ...prev,
          status: 'failed',
          message: message.data.error || 'Generation failed'
        } : null);
      }
    });
  }, []);

  const updateAgentStatus = useCallback((currentStep: string, progress: number, message: string) => {
    setAgents(prev => prev.map(agent => {
      switch (currentStep) {
        case 'profiling':
          if (agent.name === AGENTS.PROFILER.name) {
            return {
              ...agent,
              status: progress >= 30 ? AGENT_STATUS.COMPLETED : AGENT_STATUS.RUNNING,
              progress: Math.min(progress * 3.33, 100), // Scale to 100%
              message: progress >= 30 ? 'Analysis completed' : message
            };
          }
          break;
        case 'dependency_analysis':
          if (agent.name === AGENTS.DEPENDENCY.name) {
            return {
              ...agent,
              status: progress >= 60 ? AGENT_STATUS.COMPLETED : AGENT_STATUS.RUNNING,
              progress: Math.max(0, (progress - 30) * 3.33), // Scale from 30-60% to 0-100%
              message: progress >= 60 ? 'Dependencies mapped' : message
            };
          }
          break;
        case 'generating':
          if (agent.name === AGENTS.GENERATOR.name) {
            return {
              ...agent,
              status: progress >= 95 ? AGENT_STATUS.COMPLETED : AGENT_STATUS.RUNNING,
              progress: Math.max(0, (progress - 60) * 2.5), // Scale from 60-100% to 0-100%
              message: progress >= 95 ? 'Generation completed' : message
            };
          }
          break;
      }
      return agent;
    }));
  }, []);

  const uploadFile = useCallback(async (file: File) => {
    if (!session) throw new Error('No active session');

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.uploadFile(session.session_id, file);
      
      if (response.success) {
        setSession(prev => prev ? {
          ...prev,
          csv_file_path: response.data?.file_path
        } : null);
        return response.data;
      } else {
        throw new Error(response.error || 'Upload failed');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session]);

  const startGeneration = useCallback(async (nRows: number, batchSize: number) => {
    if (!session) throw new Error('No active session');

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.startGeneration(session.session_id, {
        domain: session.domain,
        user_context: session.user_context,
        n_rows: nRows,
        batch_size: batchSize
      });

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Generation failed to start');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Generation failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session]);

  const getDataPreview = useCallback(async (limit: number = 10) => {
    if (!session) throw new Error('No active session');

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.getDataPreview(session.session_id, limit);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get data preview');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session]);

  const getQualityMetrics = useCallback(async () => {
    if (!session) throw new Error('No active session');

    setLoading(true);
    setError(null);

    try {
      const response = await apiService.getQualityMetrics(session.session_id);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Failed to get quality metrics');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [session]);

  const fetchTokenUsage = useCallback(async () => {
    if (!session?.session_id) return;

    try {
      const response = await apiService.getTokenUsage(session.session_id);
      if (response.success) {
        setTokenUsage(response.data);

        // Update individual agent token usage
        setAgents(prev => prev.map(agent => {
          let tokens = 0;
          const agentData = response.data.agents || {};

          // Map frontend agent names to backend agent names
          if (agent.name === 'Profiler Agent') {
            tokens = agentData['ProfilerAgent']?.tokens_used || agentData['Profiler Agent']?.tokens_used || 0;
          } else if (agent.name === 'Dependency Agent') {
            tokens = agentData['DependencyAgent']?.tokens_used || agentData['Dependency Agent']?.tokens_used || 0;
          } else if (agent.name === 'Generator Agent') {
            tokens = agentData['GeneratorAgent']?.tokens_used || agentData['Generator Agent']?.tokens_used || 0;
          }

          return {
            ...agent,
            tokens_used: tokens
          };
        }));
      }
    } catch (error) {
      console.error('Failed to fetch token usage:', error);
    }
  }, [session]);

  const cleanup = useCallback(() => {
    webSocketService.disconnect();
    setSession(null);
    setAgents(prev => prev.map(agent => ({
      ...agent,
      status: AGENT_STATUS.IDLE,
      progress: 0,
      tokens_used: 0
    })));
    setTokenUsage(null);
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    session,
    agents,
    tokenUsage,
    loading,
    error,
    createSession,
    uploadFile,
    startGeneration,
    getDataPreview,
    getQualityMetrics,
    fetchTokenUsage,
    cleanup
  };
}
