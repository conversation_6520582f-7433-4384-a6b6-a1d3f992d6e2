// DataEcho API Service

import { API_BASE_URL } from '@/constants';
import type { 
  Session, 
  TokenUsage, 
  GenerationConfig, 
  GenerationResult,
  ApiResponse,
  SessionResponse,
  TokenUsageResponse,
  GenerationResponse
} from '@/types';

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error('API request failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Session Management
  async createSession(domain: string, userContext: string): Promise<SessionResponse> {
    return this.request<Session>('/api/v1/sessions', {
      method: 'POST',
      body: JSON.stringify({ domain, user_context: userContext }),
    });
  }

  async getSession(sessionId: string): Promise<SessionResponse> {
    return this.request<Session>(`/api/v1/sessions/${sessionId}/status`);
  }

  async deleteSession(sessionId: string): Promise<ApiResponse> {
    return this.request(`/api/v1/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // File Upload
  async uploadFile(sessionId: string, file: File): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${this.baseUrl}/api/v1/sessions/${sessionId}/upload-csv`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Upload failed' 
      };
    }
  }

  // Data Generation
  async startGeneration(
    sessionId: string, 
    config: GenerationConfig
  ): Promise<GenerationResponse> {
    return this.request<GenerationResult>(`/api/v1/sessions/${sessionId}/generate`, {
      method: 'POST',
      body: JSON.stringify({
        n_rows: config.n_rows,
        batch_size: config.batch_size,
      }),
    });
  }

  // Results
  async getResults(sessionId: string): Promise<GenerationResponse> {
    return this.request<GenerationResult>(`/api/v1/sessions/${sessionId}/results`);
  }

  async getDataPreview(sessionId: string, limit: number = 10): Promise<ApiResponse<any>> {
    return this.request(`/api/v1/sessions/${sessionId}/preview?limit=${limit}`);
  }

  async getQualityMetrics(sessionId: string): Promise<ApiResponse<any>> {
    return this.request(`/api/v1/sessions/${sessionId}/quality`);
  }

  async downloadResults(sessionId: string): Promise<Blob | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/sessions/${sessionId}/download`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Download failed:', error);
      return null;
    }
  }

  // Token Usage
  async getTokenUsage(sessionId: string): Promise<TokenUsageResponse> {
    return this.request<TokenUsage>(`/api/v1/sessions/${sessionId}/tokens`);
  }

  async getTotalTokenUsage(): Promise<TokenUsageResponse> {
    return this.request<TokenUsage>('/api/v1/tokens/total');
  }

  // Complete Workflow (for testing)
  async runCompleteWorkflow(
    domain: string,
    userContext: string,
    nRows: number,
    batchSize: number,
    file: File
  ): Promise<Blob | null> {
    const formData = new FormData();
    formData.append('domain', domain);
    formData.append('user_context', userContext);
    formData.append('n_rows', nRows.toString());
    formData.append('batch_size', batchSize.toString());
    formData.append('file', file);

    try {
      const response = await fetch(`${this.baseUrl}/api/v1/run-complete-workflow`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.blob();
    } catch (error) {
      console.error('Complete workflow failed:', error);
      return null;
    }
  }
}

export const apiService = new ApiService();
