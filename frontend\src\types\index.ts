// DataEcho Frontend Types

export interface Session {
  session_id: string;
  domain: string;
  user_context: string;
  status: 'created' | 'running' | 'completed' | 'failed';
  current_step: string;
  progress: number;
  message: string;
  created_at: string;
  updated_at: string;
  data: Record<string, any>;
  csv_file_path?: string;
}

export interface AgentStatus {
  name: string;
  status: 'idle' | 'running' | 'completed' | 'failed';
  progress: number;
  message: string;
  tokens_used: number;
  icon: string;
  color: string;
}

export interface TokenUsage {
  total_tokens: number;
  profiler_tokens: number;
  dependency_tokens: number;
  generator_tokens: number;
  cost_estimate: number;
  session_id: string;
  timestamp: string;
}

export interface FileUpload {
  file: File;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface GenerationConfig {
  domain: string;
  user_context: string;
  n_rows: number;
  batch_size: number;
}

export interface GenerationResult {
  session_id: string;
  total_generated: number;
  quality_score: number;
  data_consistency: number;
  generation_time: string;
  preview_data: any[];
  download_url: string;
}

export interface WebSocketMessage {
  type: 'status_update' | 'progress' | 'completion' | 'error';
  session_id: string;
  data: any;
}

export interface QualityMetrics {
  overall_quality: number;
  data_consistency: number;
  generation_time: string;
  rows_generated: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface SessionResponse extends ApiResponse<Session> {}
export interface TokenUsageResponse extends ApiResponse<TokenUsage> {}
export interface GenerationResponse extends ApiResponse<GenerationResult> {}
