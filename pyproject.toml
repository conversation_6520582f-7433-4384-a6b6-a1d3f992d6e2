[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/dataecho"]

[project]
name = "dataecho"
version = "1.0.0"
description = "AI-Powered Multi-Agent Synthetic Data Generation Platform"
authors = [
    {name = "Publicis Sapient", email = "<EMAIL>"},
]
keywords = ["synthetic-data", "ai", "multi-agent", "data-generation", "privacy"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Testing",
]
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.115.14",
    "uvicorn[standard]>=0.35.0",
    "python-multipart>=0.0.20",
    "aiofiles>=24.1.0",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.10.1",
    "langchain-openai>=0.3.27",
    "langchain-core>=0.3.67",
    "langgraph>=0.5.0",
    "pandas>=2.3.0",
    "python-dotenv>=1.1.1",
]

[project.optional-dependencies]
dev = [
    # Code quality
    "ruff>=0.1.9",
    "black>=23.12.0",
    "isort>=5.13.0",
    "mypy>=1.8.0",

    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.26.0",  # For FastAPI testing
    "pytest-mock>=3.12.0",

    # Development tools
    "pre-commit>=3.6.0",
    "bandit>=1.7.5",  # Security linting
    "safety>=2.3.0",  # Dependency vulnerability scanning
]

[project.urls]
Homepage = "https://github.com/publicissapient/dataecho"
Repository = "https://github.com/publicissapient/dataecho.git"
Documentation = "https://dataecho.readthedocs.io"
"Bug Tracker" = "https://github.com/publicissapient/dataecho/issues"

[project.scripts]
dataecho = "dataecho.main:main"

# Ruff configuration (replaces flake8, isort, and some other tools)
[tool.ruff]
target-version = "py312"
line-length = 88
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG", # flake8-unused-arguments
    "COM", # flake8-commas
    "ICN", # flake8-import-conventions
    "PIE", # flake8-pie
    "PL",  # pylint
    "PT",  # flake8-pytest-style
    "PTH", # flake8-use-pathlib
    "RET", # flake8-return
    "RUF", # Ruff-specific rules
    "SIM", # flake8-simplify
    "T20", # flake8-print
    "TCH", # flake8-type-checking
    "TID", # flake8-tidy-imports
    "TRY", # tryceratops
]
ignore = [
    "E501",   # line too long, handled by black
    "B008",   # do not perform function calls in argument defaults
    "C901",   # too complex
    "RET504", # unnecessary variable assignment before return
    "PLR0913", # too many arguments
    "COM812", # missing trailing comma (conflicts with formatter)
    "ISC001", # implicitly concatenated string literals (conflicts with formatter)
]

[tool.ruff.per-file-ignores]
"tests/**/*.py" = [
    "ARG", # Unused function args -> fixtures nevertheless are functionally relevant
    "FBT", # Don't care about booleans as positional arguments in tests
    "PLR2004", # Magic value used in comparison, ...
    "S101", # asserts allowed in tests...
    "TID252", # Prefer absolute imports over relative imports from parent modules
]

[tool.ruff.isort]
known-first-party = ["dataecho", "backend"]

# Black configuration
[tool.black]
target-version = ['py312']
line-length = 88
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# MyPy configuration
[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "langgraph.*",
    "langchain_openai.*",
    "langchain_core.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=src/dataecho",
    "--cov=backend",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

# Coverage configuration
[tool.coverage.run]
source = ["src/dataecho", "backend"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/__pycache__/*",
    "*/venv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Bandit security linting
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process
