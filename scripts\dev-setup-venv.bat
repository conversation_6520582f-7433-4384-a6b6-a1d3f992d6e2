@echo off
REM DataEcho Development Setup Script for Windows with Virtual Environment
echo =====================================
echo DataEcho Development Environment Setup
echo (Virtual Environment Version)
echo =====================================

REM Change to the project root directory (parent of scripts)
cd /d "%~dp0.."

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.12+ from https://python.org
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Found Python %PYTHON_VERSION%

REM Create virtual environment if it doesn't exist
if not exist venv (
    echo.
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
    echo Virtual environment created successfully
) else (
    echo Virtual environment already exists
)

REM Activate virtual environment
echo.
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

REM Upgrade pip first
echo.
echo Upgrading pip...
python -m pip install --upgrade pip

REM Check if UV is installed in virtual environment
echo.
echo Checking UV package manager...
python -m uv --version >nul 2>&1
if errorlevel 1 (
    echo Installing UV package manager in virtual environment...
    pip install uv
    if errorlevel 1 (
        echo ERROR: Failed to install UV
        pause
        exit /b 1
    )
) else (
    echo UV package manager found
)

REM Install dependencies using UV in virtual environment
echo.
echo Installing dependencies in virtual environment...
python -m uv pip install -e ".[dev]"
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo Trying with regular pip as fallback...
    pip install -e ".[dev]"
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies with pip as well
        pause
        exit /b 1
    )
)

REM Note: Pre-commit hooks are optional and may cause setup issues
REM To install manually after setup: pre-commit install
echo.
echo Skipping pre-commit hooks setup (can be installed manually later)

REM Create .env file if it doesn't exist
if not exist .env (
    echo.
    echo Creating .env file from template...
    copy .env.example .env
    echo .env file created. Please edit it with your API keys.
) else (
    echo .env file already exists
)

REM Create necessary directories
if not exist uploads mkdir uploads
if not exist sessions mkdir sessions  
if not exist results mkdir results

echo.
echo =====================================
echo Development environment setup complete!
echo =====================================
echo.
echo IMPORTANT: Virtual environment is now active.
echo To use DataEcho in future sessions, activate it with:
echo    venv\Scripts\activate.bat
echo.
echo Next steps:
echo 1. Edit .env file with your API keys:
echo    - OPENROUTER_API_KEY or OPENAI_API_KEY
echo 2. Run the development server:
echo    - scripts\run-dev.bat (will auto-activate venv)
echo 3. Visit http://localhost:8000/docs for API documentation
echo.
echo Current session: Virtual environment is ACTIVE
echo.
pause