@echo off
REM Clean and rebuild Docker for DataEcho
echo ========================================
echo Docker Clean and Rebuild for DataEcho
echo ========================================

REM Change to the project root directory
cd /d "%~dp0.."

echo Stopping and removing all containers...
docker-compose down --rmi all --volumes --remove-orphans

echo.
echo Cleaning Docker system...
docker system prune -af

echo.
echo Removing any problematic directories...
if exist env rmdir /s /q env 2>nul
if exist venv rmdir /s /q venv 2>nul
if exist .venv rmdir /s /q .venv 2>nul

echo.
echo Checking .env file...
if not exist .env (
    echo Creating .env file from template...
    copy .env.example .env
    echo.
    echo ⚠️  IMPORTANT: Please edit .env file with your API keys
    echo    Required: OPENROUTER_API_KEY or OPENAI_API_KEY
    echo.
    pause
)

echo.
echo Building and starting fresh containers...
docker-compose up --build -d

if errorlevel 1 (
    echo.
    echo ❌ Build failed. Showing logs:
    docker-compose logs
    pause
    exit /b 1
)

echo.
echo ✅ Docker rebuild complete!
echo.
echo Services should be running at:
echo - API: http://localhost:8000
echo - Docs: http://localhost:8000/docs
echo.
echo Check status with: docker-compose ps
echo View logs with: docker-compose logs -f
echo.
pause