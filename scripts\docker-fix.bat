@echo off
REM Fix Docker build issues by cleaning up problematic directories
echo ========================================
echo Docker Build Fix for Windows
echo ========================================

REM Change to the project root directory (parent of scripts)
cd /d "%~dp0.."

echo Stopping any running containers...
docker-compose down 2>nul

echo.
echo Cleaning up virtual environments and cache directories...
if exist env (
    echo Removing env directory...
    rmdir /s /q env
)
if exist venv (
    echo Removing venv directory...
    rmdir /s /q venv
)
if exist .venv (
    echo Removing .venv directory...
    rmdir /s /q .venv
)
if exist __pycache__ (
    echo Removing __pycache__ directories...
    for /d /r . %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"
)
if exist .pytest_cache (
    echo Removing .pytest_cache directories...
    for /d /r . %%d in (.pytest_cache) do @if exist "%%d" rmdir /s /q "%%d"
)

echo.
echo Removing any existing Docker images...
docker-compose down --rmi all 2>nul

echo.
echo Pruning Docker system...
docker system prune -f

echo.
echo ✅ Cleanup complete!
echo Now try running: scripts\docker-setup.bat
echo.
pause