@echo off
REM Quick Docker restart script
echo ========================================
echo Docker Restart for DataEcho
echo ========================================

REM Change to the project root directory
cd /d "%~dp0.."

echo Stopping containers...
docker-compose down

echo.
echo Rebuilding and starting containers...
docker-compose up --build -d

if errorlevel 1 (
    echo.
    echo ❌ Restart failed. Showing logs:
    docker-compose logs
    pause
    exit /b 1
)

echo.
echo ✅ Docker restart complete!
echo.
echo Check status: docker-compose ps
echo View logs: docker-compose logs -f
echo.
pause