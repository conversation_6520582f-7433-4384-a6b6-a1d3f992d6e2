@echo off
REM Docker setup script for Windows
echo =====================================
echo DataEcho Docker Setup for Windows
echo =====================================

REM Change to the project root directory (parent of scripts)
cd /d "%~dp0.."

REM Check if Docker is running
docker version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not running or not installed
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)

echo ✓ Docker is running

REM Check if .env file exists
if not exist .env (
    echo Creating .env file from template...
    copy .env.example .env
    echo.
    echo ⚠️  IMPORTANT: Please edit .env file with your API keys before continuing
    echo    Required: OPENROUTER_API_KEY or OPENAI_API_KEY
    echo.
    set /p continue="Press Enter after updating .env file, or Ctrl+C to exit: "
)

REM Clean up any problematic virtual environment directories
echo Cleaning up virtual environments (if any)...
if exist env rmdir /s /q env 2>nul
if exist venv rmdir /s /q venv 2>nul
if exist .venv rmdir /s /q .venv 2>nul

REM Create necessary directories
if not exist uploads mkdir uploads
if not exist sessions mkdir sessions  
if not exist results mkdir results

echo.
echo Building and starting DataEcho services...
echo This may take a few minutes on first run...
echo Note: Ignoring virtual environments and development files
echo.

REM Build and start services
docker-compose up --build -d

if errorlevel 1 (
    echo.
    echo ❌ Failed to start services
    echo Checking for common issues...
    echo.
    docker-compose logs
    pause
    exit /b 1
)

echo.
echo =====================================
echo ✅ DataEcho is running in Docker!
echo =====================================
echo.
echo Services:
echo - API Server: http://localhost:8000
echo - API Docs:   http://localhost:8000/docs
echo - Postgres:   localhost:5432
echo.
echo To check status: docker-compose ps
echo To view logs:    docker-compose logs -f
echo To stop:         docker-compose down
echo.
pause