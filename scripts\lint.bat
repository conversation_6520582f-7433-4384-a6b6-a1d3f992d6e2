@echo off
REM Run code quality checks
echo Running code quality checks...

REM Change to the project root directory (parent of scripts)
cd /d "%~dp0.."
echo.

echo Running Ruff linting...
ruff check .
if errorlevel 1 (
    echo Linting failed. Please fix the issues above.
    pause
    exit /b 1
)

echo.
echo Running type checking...
mypy src\dataecho\ backend\
if errorlevel 1 (
    echo Type checking failed. Please fix the issues above.
    pause
    exit /b 1
)

echo.
echo All checks passed!
pause