# PowerShell helper script for DataEcho quick test
# This script performs a complete workflow test with sample data

param(
    [string]$ServerUrl = "http://localhost:8000"
)

Write-Host "DataEcho Quick Test with Sample Data" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Server URL: $ServerUrl" -ForegroundColor White
Write-Host ""

# Change to the project root directory (parent of scripts)
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
Set-Location $ProjectRoot

# Step 1: Check if server is running
Write-Host "1. Checking server health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$ServerUrl/health" -Method GET -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "   Server is running" -ForegroundColor Green
    } else {
        Write-Host "   Server returned status code: $($response.StatusCode)" -ForegroundColor Red
        Write-Host "[ERROR] Server is not responding properly" -ForegroundColor Red
        Write-Host "Please start the server first using: scripts\run-dev-venv.bat" -ForegroundColor Yellow
        Read-Host "Press Enter to continue"
        exit 1
    }
} catch {
    Write-Host "   Server is not running at $ServerUrl" -ForegroundColor Red
    Write-Host "[ERROR] Cannot connect to server" -ForegroundColor Red
    Write-Host "Please start the server first using: scripts\run-dev-venv.bat" -ForegroundColor Yellow
    Read-Host "Press Enter to continue"
    exit 1
}

# Step 2: Check if sample data exists
Write-Host "2. Checking sample data..." -ForegroundColor Yellow
$SampleDataFile = "sample_trading_data.csv"
if (-not (Test-Path $SampleDataFile)) {
    Write-Host "   Sample data file not found: $SampleDataFile" -ForegroundColor Red
    Write-Host "   Trying alternative location: tests\fixtures\sample_data.csv" -ForegroundColor Yellow
    $SampleDataFile = "tests\fixtures\sample_data.csv"
    if (-not (Test-Path $SampleDataFile)) {
        Write-Host "   Sample data file not found: $SampleDataFile" -ForegroundColor Red
        Write-Host "[ERROR] Sample data missing" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        exit 1
    }
}
Write-Host "   Sample data found: $SampleDataFile" -ForegroundColor Green

# Step 3: Execute the workflow
Write-Host "3. Executing data generation workflow..." -ForegroundColor Yellow

# Create output filename with timestamp
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$OutputFile = "generated_data_$Timestamp.csv"

# Prepare multipart form data
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

# Prepare the form data using the working approach
$sampleFile = Get-Item $SampleDataFile
$bodyLines = (
    "--$boundary",
    "Content-Disposition: form-data; name=`"domain`"$LF",
    "trading_data",
    "--$boundary",
    "Content-Disposition: form-data; name=`"user_context`"$LF", 
    "Trading data with price, volume, and market indicators",
    "--$boundary",
    "Content-Disposition: form-data; name=`"n_rows`"$LF",
    "25",
    "--$boundary", 
    "Content-Disposition: form-data; name=`"batch_size`"$LF",
    "5",
    "--$boundary",
    "Content-Disposition: form-data; name=`"file`"; filename=`"$($sampleFile.Name)`"",
    "Content-Type: text/csv$LF",
    [System.IO.File]::ReadAllText($sampleFile.FullName),
    "--$boundary--$LF"
) -join $LF

try {
    Write-Host "   Sending request to API..." -ForegroundColor White
    
    # Execute the workflow
    $response = Invoke-WebRequest -Uri "$ServerUrl/api/v1/run-complete-workflow" `
        -Method POST `
        -ContentType "multipart/form-data; boundary=$boundary" `
        -Body $bodyLines `
        -TimeoutSec 300
    
    if ($response.StatusCode -eq 200) {
        # Save the response content to file using the working approach
        [System.IO.File]::WriteAllText($OutputFile, $response.Content, [System.Text.Encoding]::UTF8)
        
        Write-Host "   Workflow completed successfully!" -ForegroundColor Green
        Write-Host "   Generated data saved to: $OutputFile" -ForegroundColor Green
        
        # Show file size
        $fileInfo = Get-Item $OutputFile
        $fileSizeKB = [math]::Round($fileInfo.Length / 1024, 2)
        Write-Host "   Generated file size: $fileSizeKB KB" -ForegroundColor White
        
        # Show preview of generated data
        Write-Host ""
        Write-Host "Preview of generated data:" -ForegroundColor Cyan
        Write-Host "========================" -ForegroundColor Cyan
        
        try {
            $csvContent = Get-Content $OutputFile -TotalCount 5
            foreach ($line in $csvContent) {
                Write-Host $line -ForegroundColor White
            }
            
            # Count total rows (excluding header)
            $totalLines = (Get-Content $OutputFile | Measure-Object -Line).Lines
            $totalRows = $totalLines - 1
            Write-Host ""
            Write-Host "Total rows generated: $totalRows" -ForegroundColor White
            
        } catch {
            Write-Host "File saved successfully but preview failed" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "   Error: Server returned status code $($response.StatusCode)" -ForegroundColor Red
        Write-Host "[ERROR] Data generation failed" -ForegroundColor Red
        Read-Host "Press Enter to continue"
        exit 1
    }
    
} catch {
    Write-Host "   Error during data generation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "[ERROR] Test failed. Please check the server status and try again." -ForegroundColor Red
    Read-Host "Press Enter to continue"
    exit 1
}

Write-Host ""
Write-Host "Test completed successfully!" -ForegroundColor Green
Read-Host "Press Enter to continue"