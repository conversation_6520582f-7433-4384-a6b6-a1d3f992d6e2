@echo off
setlocal enabledelayedexpansion

REM Quick test script for DataEcho with sample data
set "SERVER_URL=http://localhost:8000"
if not "%1"=="" set "SERVER_URL=%1"

REM Change to the project root directory (parent of scripts)
cd /d "%~dp0.."

REM Call the PowerShell helper script
powershell -NoProfile -ExecutionPolicy Bypass -File "%~dp0quick-test-helper.ps1" -ServerUrl "%SERVER_URL%"

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Test failed! Please check the server status and try again.
    pause
    exit /b 1
)

echo.
pause