#!/bin/bash
# Quick test script for DataEcho with sample data
# Compatible with Linux and macOS

set -e  # Exit on any error

# Default server URL
SERVER_URL="http://localhost:8000"
if [ ! -z "$1" ]; then
    SERVER_URL="$1"
fi

echo "DataEcho Quick Test with Sample Data"
echo "===================================="
echo "Server URL: $SERVER_URL"
echo ""

# Change to the project root directory (parent of scripts)
cd "$(dirname "$0")/.."

# Function to make HTTP requests with error handling
make_request() {
    local url="$1"
    local method="${2:-GET}"
    local data="$3"
    local content_type="$4"
    
    if command -v curl >/dev/null 2>&1; then
        if [ "$method" = "GET" ]; then
            curl -s -f "$url" --connect-timeout 5
        else
            if [ ! -z "$content_type" ]; then
                curl -s -f -X "$method" -H "Content-Type: $content_type" -d "$data" "$url"
            else
                curl -s -f -X "$method" -d "$data" "$url"
            fi
        fi
    else
        echo "Error: curl is required but not installed"
        exit 1
    fi
}

# Step 1: Check if server is running
echo "1. Checking server health..."
if ! make_request "$SERVER_URL/health" >/dev/null 2>&1; then
    echo "✗ Server is not running at $SERVER_URL"
    echo "Please start the server first using: make run-dev"
    exit 1
fi
echo "✓ Server is running"

# Step 2: Check if sample data exists
echo "2. Checking sample data..."
SAMPLE_DATA="sample_trading_data.csv"
if [ ! -f "$SAMPLE_DATA" ]; then
    echo "✗ Sample data file not found: $SAMPLE_DATA"
    exit 1
fi
echo "✓ Sample data found"

# Step 3: Execute the workflow
echo "3. Executing data generation workflow..."

# Create output filename with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="generated_data_$TIMESTAMP.csv"

# Execute the workflow using curl with multipart form data
if curl -s -f -X POST "$SERVER_URL/api/v1/run-complete-workflow" \
    -F "domain=trading_data" \
    -F "user_context=Trading data with price, volume, and market indicators" \
    -F "n_rows=25" \
    -F "batch_size=5" \
    -F "file=@$SAMPLE_DATA" \
    --output "$OUTPUT_FILE" \
    --connect-timeout 300; then
    
    echo "✓ Workflow completed successfully!"
    echo "Generated data saved to: $OUTPUT_FILE"
    
    # Show file size
    if command -v stat >/dev/null 2>&1; then
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            SIZE=$(stat -f%z "$OUTPUT_FILE")
        else
            # Linux
            SIZE=$(stat -c%s "$OUTPUT_FILE")
        fi
        SIZE_KB=$((SIZE / 1024))
        echo "Generated file size: ${SIZE_KB} KB"
    fi
    
    # Show preview of generated data
    echo ""
    echo "Preview of generated data:"
    echo "========================"
    head -n 5 "$OUTPUT_FILE"
    
    # Count total rows (excluding header)
    if command -v wc >/dev/null 2>&1; then
        TOTAL_ROWS=$(($(wc -l < "$OUTPUT_FILE") - 1))
        echo ""
        echo "Total rows generated: $TOTAL_ROWS"
    fi
    
else
    echo "✗ Error during data generation"
    echo "Please check the server logs for more details"
    exit 1
fi

echo ""
echo "Test completed successfully!"
echo "Press Enter to continue..."
read -r