@echo off
REM Start DataEcho development server with virtual environment
echo Starting DataEcho development server (Virtual Environment)...

REM Change to the project root directory (parent of scripts)
cd /d "%~dp0.."

REM Check if virtual environment exists
if not exist venv (
    echo ERROR: Virtual environment not found!
    echo Please run scripts\dev-setup-venv.bat first to create it.
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist .env (
    echo WARNING: .env file not found!
    echo Please run scripts\dev-setup-venv.bat first to create it.
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ERROR: Failed to activate virtual environment
    pause
    exit /b 1
)

echo.
echo Virtual environment activated successfully!
echo Visit http://localhost:8000/docs for API documentation
echo Press Ctrl+C to stop the server
echo.

REM Start the development server
uvicorn backend.api_service.main:app --reload --host 0.0.0.0 --port 8000