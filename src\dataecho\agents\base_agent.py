# File: app/agents/base_agent.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from dataecho.models.agents import AgentState, AgentStatus, AgentType
from dataecho.services.llm_service import LLMService
from dataecho.utils.logger import setup_logger
from datetime import datetime
import uuid

logger = setup_logger(__name__)

class BaseAgent(ABC):
    """Base class for all agents"""
    
    def __init__(self, agent_type: AgentType, session_id: str):
        self.agent_id = str(uuid.uuid4())
        self.agent_type = agent_type
        self.session_id = session_id
        self.llm_service = LLMService()
        self.state = AgentState(
            agent_id=self.agent_id,
            agent_type=agent_type,
            status=AgentStatus.PENDING,
            session_id=session_id,
            config=self._get_default_config(),
            input_data={},
            created_at=datetime.utcnow().isoformat(),
            updated_at=datetime.utcnow().isoformat()
        )
    
    @abstractmethod
    def _get_default_config(self):
        """Get default configuration for this agent"""
        pass
    
    @abstractmethod
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and return results"""
        pass
    
    async def run(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run the agent with error handling"""
        try:
            self.state.status = AgentStatus.RUNNING
            self.state.input_data = input_data
            self.state.updated_at = datetime.utcnow().isoformat()
            
            logger.info(f"Starting {self.agent_type} agent: {self.agent_id}")
            
            result = await self.process(input_data)
            
            self.state.status = AgentStatus.COMPLETED
            self.state.output_data = result
            self.state.updated_at = datetime.utcnow().isoformat()
            
            logger.info(f"Completed {self.agent_type} agent: {self.agent_id}")
            
            return result
            
        except Exception as e:
            self.state.status = AgentStatus.FAILED
            self.state.error_message = str(e)
            self.state.updated_at = datetime.utcnow().isoformat()
            
            logger.error(f"Failed {self.agent_type} agent: {self.agent_id} - {str(e)}")
            raise
    
    def get_state(self) -> AgentState:
        """Get current agent state"""
        return self.state

