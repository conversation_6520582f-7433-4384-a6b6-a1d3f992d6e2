# File: app/agents/dependency_agent.py
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from typing import Dict, Any
from dataecho.agents.base_agent import BaseAgent
from dataecho.models.agents import AgentType, AgentConfig
from dataecho.utils.data_utils import extract_and_parse_json
from dataecho.prompts import DEPENDENCY_SYSTEM_PROMPT2, DEPENDENCY_USER_PROMPT2

class DependencyAgent(BaseAgent):
    """Agent for analyzing data dependencies"""
    
    def __init__(self, session_id: str):
        super().__init__(AgentType.DEPENDENCY, session_id)
        self.memory = MemorySaver()
    
    def _get_default_config(self) -> AgentConfig:
        return AgentConfig(
            agent_type=AgentType.DEPENDENCY,
            model_name="deepseek/deepseek-prover-v2:free",
            temperature=1.0,
            max_retries=2
        )
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze dependencies based on profile data"""
        profile_data = input_data.get("profile_data")
        user_context = input_data.get("user_context", "")
        
        # Format prompts
        formatted_user_prompt = DEPENDENCY_USER_PROMPT2.format(
            profiler_json=profile_data,
            user_context=user_context
        )
        
        # Create messages
        input_messages = [
            SystemMessage(content=DEPENDENCY_SYSTEM_PROMPT2),
            HumanMessage(content=formatted_user_prompt)
        ]
        
        # Configure LLM
        config = {"configurable": {"thread_id": f"dependency-{self.session_id}"}}
        
        # Get response with token tracking
        output = await self.llm_service.invoke_with_messages(
            input_messages, config, self.memory, self.session_id, "DependencyAgent"
        )
        
        # Parse JSON response
        dependency_data = extract_and_parse_json(output["messages"][-1].content)
        
        return {
            "dependencies": dependency_data
        }

