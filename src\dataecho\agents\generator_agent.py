# File: app/agents/generator_agent.py
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.memory import MemorySaver
from typing import Dict, Any, List
from dataecho.agents.base_agent import BaseAgent
from dataecho.models.agents import AgentType, AgentConfig
from dataecho.utils.data_utils import extract_and_parse_json
from dataecho.prompts import GENERATOR_SYSTEM_PROMPT, GENERATOR_USER_PROMPT
from dataecho.websocket.connection_manager import ConnectionManager

class GeneratorAgent(BaseAgent):
    """Agent for generating synthetic data"""
    
    def __init__(self, session_id: str, connection_manager: ConnectionManager = None):
        super().__init__(AgentType.GENERATOR, session_id)
        self.memory = MemorySaver()
        self.connection_manager = connection_manager
    
    def _get_default_config(self) -> AgentConfig:
        return AgentConfig(
            agent_type=AgentType.GENERATOR,
            model_name="deepseek/deepseek-prover-v2:free",
            temperature=1.0,
            max_retries=2
        )
    
    async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate synthetic data in batches"""
        profile_data = input_data.get("profile_data")
        dependency_data = input_data.get("dependency_data")
        user_context = input_data.get("user_context", "")
        n_rows = input_data.get("n_rows", 20)
        batch_size = input_data.get("batch_size", 10)
        
        # Format initial prompts
        formatted_system_prompt = GENERATOR_SYSTEM_PROMPT.format(
            profiler_json=profile_data,
            dependency_json=dependency_data
        )
        formatted_user_prompt = GENERATOR_USER_PROMPT.format(
            N=n_rows,
            user_context=user_context
        )
        
        # Create initial messages
        input_messages = [
            SystemMessage(content=formatted_system_prompt),
            HumanMessage(content=formatted_user_prompt)
        ]
        
        # Configure LLM
        config = {"configurable": {"thread_id": f"generator-{self.session_id}"}}
        
        # Generate initial batch
        results = []
        error_data = []
        retry_count = 3
        
        # Send initial progress update
        if self.connection_manager:
            await self.connection_manager.send_progress_update(
                self.session_id, 0, n_rows, "Starting data generation..."
            )
        
        try:
            # Initial generation
            initial_response = await self.llm_service.invoke_with_messages(input_messages, config, self.memory)
            initial_data = extract_and_parse_json(initial_response["messages"][-1].content)
            
            if initial_data and isinstance(initial_data, list):
                results.extend(initial_data)
            
            # Send progress update
            if self.connection_manager:
                await self.connection_manager.send_progress_update(
                    self.session_id, len(results), n_rows, f"Generated {len(results)} rows..."
                )
            
            # Continue generating in batches if needed
            count = 0
            max_batches = 10  # Prevent infinite loops
            
            while len(results) < n_rows and retry_count > 0 and count < max_batches:
                remaining_rows = n_rows - len(results)
                current_batch_size = min(batch_size, remaining_rows)
                
                continuation_prompt = f"""Continue generating {current_batch_size} more rows of data. 
                Total needed: {n_rows}, Already generated: {len(results)}, 
                Remaining: {remaining_rows}. 
                Return only the JSON array of new rows, no additional text."""
                
                continuation_message = [HumanMessage(content=continuation_prompt)]
                
                try:
                    batch_response = await self.llm_service.invoke_with_messages(
                        continuation_message, config, self.memory
                    )
                    batch_data = extract_and_parse_json(batch_response["messages"][-1].content)
                    
                    if batch_data and isinstance(batch_data, list):
                        results.extend(batch_data[:remaining_rows])  # Don't exceed target
                    
                    # Send progress update
                    if self.connection_manager:
                        await self.connection_manager.send_progress_update(
                            self.session_id, len(results), n_rows, 
                            f"Generated {len(results)}/{n_rows} rows..."
                        )
                    
                except Exception as e:
                    retry_count -= 1
                    error_data.append(str(e))
                    logger.error(f"Error in batch generation: {str(e)}")
                
                count += 1
            
            # Final progress update
            if self.connection_manager:
                await self.connection_manager.send_progress_update(
                    self.session_id, len(results), n_rows, "Data generation completed!"
                )
            
            return {
                "generated_data": results[:n_rows],  # Ensure we don't exceed target
                "total_generated": len(results),
                "target_rows": n_rows,
                "batches_processed": count,
                "errors": error_data
            }
            
        except Exception as e:
            if self.connection_manager:
                await self.connection_manager.send_error(self.session_id, f"Generation failed: {str(e)}")
            raise



# # File: app/agents/generator_agent.py
# from langchain_core.messages import HumanMessage, SystemMessage
# from langgraph.checkpoint.memory import MemorySaver
# from typing import Dict, Any, List
# from datetime import datetime
# import asyncio
# from dataecho.agents.base_agent import BaseAgent
# from dataecho.models.agents import AgentType, AgentConfig
# from dataecho.utils.data_utils import extract_and_parse_json
# from dataecho.prompts import GENERATOR_SYSTEM_PROMPT, GENERATOR_USER_PROMPT
# from dataecho.websocket.connection_manager import ConnectionManager
# import logging

# logger = logging.getLogger(__name__)

# class GeneratorAgent(BaseAgent):
#     """Agent for generating synthetic data"""
    
#     def __init__(self, session_id: str, connection_manager: ConnectionManager = None):
#         super().__init__(AgentType.GENERATOR, session_id)
#         self.memory = MemorySaver()
#         self.connection_manager = connection_manager
    
#     def _get_default_config(self) -> AgentConfig:
#         return AgentConfig(
#             agent_type=AgentType.GENERATOR,
#             model_name="deepseek/deepseek-prover-v2:free",
#             temperature=1.0,
#             max_retries=2
#         )
    
#     async def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
#         """Generate synthetic data in batches matching GeneratorResult interface"""
#         profile_data = input_data.get("profile_data")
#         dependency_data = input_data.get("dependency_data")
#         user_context = input_data.get("user_context", "")
#         n_rows = input_data.get("n_rows", 20)
#         batch_size = input_data.get("batch_size", 10)
        
#         # Validate inputs
#         if n_rows <= 0:
#             raise ValueError("n_rows must be greater than 0")
#         if batch_size <= 0:
#             raise ValueError("batch_size must be greater than 0")
        
#         # Calculate total batches needed based on target rows and batch size
#         total_batches = (n_rows + batch_size - 1) // batch_size  # Ceiling division
        
#         logger.info(f"Planning data generation: {n_rows} target rows, {batch_size} per batch = {total_batches} batches needed")
        
#         # Format initial prompts
#         formatted_system_prompt = GENERATOR_SYSTEM_PROMPT.format(
#             profiler_json=profile_data,
#             dependency_json=dependency_data
#         )

#         formatted_user_prompt = GENERATOR_USER_PROMPT.format(
#             N=n_rows,
#             user_context=user_context
#         )
        
#         # Configure LLM
#         config = {"configurable": {"thread_id": f"generator-{self.session_id}"}}
        
#         # Initialize result structure matching GeneratorResult interface
#         result = {
#             "batches": [],
#             "total_rows": 0,
#             "completed_batches": 0,
#             "total_batches": total_batches
#         }
        
#         # Create initial messages
#         input_messages = [
#             SystemMessage(content=formatted_system_prompt),
#             HumanMessage(content=formatted_user_prompt.format(
#                 N=min(batch_size, n_rows),  # First batch size
#                 user_context=user_context
#             ))
#         ]
        
#         # Send initial progress update
#         if self.connection_manager:
#             await self.connection_manager.send_progress_update(
#                 self.session_id, 0, n_rows, "Starting data generation..."
#             )
        
#         try:
#             batch_id = 1
#             total_generated = 0
#             max_retries = 3
            
#             # Process each batch until we have enough rows or complete all planned batches
#             while total_generated < n_rows and batch_id <= total_batches:
#                 # Calculate how many rows needed for this specific batch
#                 remaining_rows = n_rows - total_generated
#                 current_batch_size = min(batch_size, remaining_rows)
                
#                 logger.info(f"Processing batch {batch_id}/{total_batches}: requesting {current_batch_size} rows (total: {total_generated}/{n_rows})")
#                 retry_count = max_retries
#                 batch_generated = False
                
#                 while retry_count > 0 and not batch_generated:
#                     try:
#                         if batch_id == 1:
#                             # First batch - use initial messages
#                             response = await self.llm_service.invoke_with_messages(
#                                 input_messages, config, self.memory
#                             )
#                         else:
#                             # Subsequent batches - use continuation prompt
#                             continuation_prompt = f"""Continue generating exactly {current_batch_size} more rows of data following the same schema and patterns established in previous batches.

# Progress: Batch {batch_id} of {total_batches}
# - Target total: {n_rows} rows
# - Already generated: {total_generated} rows  
# - This batch: {current_batch_size} rows
# - Remaining after this batch: {n_rows - total_generated - current_batch_size} rows

# Return only a JSON array containing exactly {current_batch_size} new data rows. No additional text or explanations."""
                            
#                             continuation_messages = [HumanMessage(content=continuation_prompt)]
#                             response = await self.llm_service.invoke_with_messages(
#                                 continuation_messages, config, self.memory
#                             )
                        
#                         # Extract and parse the generated data
#                         batch_data = extract_and_parse_json(response["messages"][-1].content)
                        
#                         if batch_data and isinstance(batch_data, list) and len(batch_data) > 0:
#                             # Ensure we don't exceed the requested batch size or total target
#                             batch_data = batch_data[:current_batch_size]
                            
#                             # Double-check we don't exceed total target
#                             if total_generated + len(batch_data) > n_rows:
#                                 batch_data = batch_data[:n_rows - total_generated]
                            
#                             # Create batch entry matching the interface
#                             batch_entry = {
#                                 "batch_id": batch_id,
#                                 "data": batch_data,
#                                 "generated_at": datetime.utcnow().isoformat() + "Z"
#                             }
                            
#                             result["batches"].append(batch_entry)
#                             total_generated += len(batch_data)
#                             result["total_rows"] = total_generated
#                             result["completed_batches"] = batch_id
                            
#                             batch_generated = True
                            
#                             # Send progress update
#                             if self.connection_manager:
#                                 progress_msg = f"Completed batch {batch_id}/{total_batches}: {len(batch_data)} rows (Total: {total_generated}/{n_rows})"
#                                 await self.connection_manager.send_progress_update(
#                                     self.session_id, 
#                                     total_generated, 
#                                     n_rows, 
#                                     progress_msg
#                                 )
                            
#                             logger.info(f"Batch {batch_id} completed: {len(batch_data)} rows generated (Total: {total_generated}/{n_rows})")
                            
#                             # Early exit if we've reached our target
#                             if total_generated >= n_rows:
#                                 logger.info(f"Target of {n_rows} rows reached. Stopping generation.")
#                                 break
                            
#                             # Small delay between batches to prevent overwhelming the API
#                             if batch_id < total_batches and total_generated < n_rows:
#                                 await asyncio.sleep(0.5)
#                         else:
#                             raise ValueError("No valid data generated in batch")
                            
#                     except Exception as e:
#                         retry_count -= 1
#                         logger.error(f"Error in batch {batch_id}, attempt {max_retries - retry_count}: {str(e)}")
                        
#                         if retry_count == 0:
#                             logger.error(f"Failed to generate batch {batch_id} after {max_retries} attempts")
#                             # Continue to next batch or break if this is critical
#                             break
                        
#                         # Wait before retry
#                         await asyncio.sleep(1)
                
#                 if batch_generated:
#                     batch_id += 1
#                 else:
#                     # Failed to generate this batch, stop trying
#                     logger.error(f"Stopping generation at batch {batch_id} due to repeated failures")
#                     break
            
#             # Final progress update with detailed summary
#             if self.connection_manager:
#                 if total_generated >= n_rows:
#                     status_msg = f"✅ Generation completed successfully! Generated {total_generated} rows in {result['completed_batches']} batches"
#                 else:
#                     status_msg = f"⚠️ Generation partially completed: {total_generated}/{n_rows} rows in {result['completed_batches']}/{total_batches} batches"
                
#                 await self.connection_manager.send_progress_update(
#                     self.session_id, total_generated, n_rows, status_msg
#                 )
            
#             # Log final summary
#             logger.info(f"Data generation summary:")
#             logger.info(f"  - Target: {n_rows} rows in {total_batches} batches of {batch_size} each")
#             logger.info(f"  - Actual: {total_generated} rows in {result['completed_batches']} completed batches")
#             logger.info(f"  - Success rate: {(total_generated/n_rows)*100:.1f}%")
            
#             return result
            
#         except Exception as e:
#             logger.error(f"Critical error in data generation: {str(e)}")
#             if self.connection_manager:
#                 await self.connection_manager.send_error(
#                     self.session_id, f"Generation failed: {str(e)}"
#                 )
            
#             # Return partial results if any were generated
#             if result["batches"]:
#                 result["error"] = str(e)
#                 return result
#             else:
#                 raise