# File: src/dataecho/config.py
from pydantic_settings import BaseSettings
from pydantic import field_validator
from typing import Optional, List

class Settings(BaseSettings):
    # OpenAI/OpenRouter Configuration
    openai_api_key: Optional[str] = None
    openrouter_api_key: Optional[str] = None
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    
    # Model Configuration
    default_model: str = "deepseek/deepseek-prover-v2:free"
    model_temperature: float = 1.0
    model_max_tokens: Optional[int] = None
    model_timeout: Optional[int] = None
    model_max_retries: int = 2
    
    @field_validator('model_max_tokens', 'model_timeout', mode='before')
    @classmethod
    def parse_optional_int(cls, v):
        """Convert empty strings to None for optional integer fields."""
        if v == '' or v is None:
            return None
        return int(v)
    
    # Application Configuration
    upload_dir: str = "uploads"
    session_storage_dir: str = "sessions"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # Database Configuration (for future MongoDB integration)
    mongodb_url: Optional[str] = None
    mongodb_db_name: str = "multiagent_data_gen"
    
    # WebSocket Configuration
    websocket_timeout: int = 300  # 5 minutes
    
    # Docker Hub Configuration (for deployment)
    dockerhub_pat: Optional[str] = None
    docker_username: Optional[str] = None
    docker_tag: str = "v1"
    
    # Development Configuration
    debug: bool = False
    log_level: str = "INFO"
    
    # CORS Configuration
    cors_origins: Optional[List[str]] = None
    cors_allow_credentials: bool = True
    
    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string to list."""
        if v is None or v == '':
            return None
        if isinstance(v, list):
            return v
        if isinstance(v, str):
            # Handle JSON-like string format
            import json
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # Handle comma-separated format
                return [origin.strip() for origin in v.split(',')]
        return v
    
    class Config:
        env_file = ".env"

settings = Settings()

