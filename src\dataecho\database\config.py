"""Database configuration for DataEcho."""

import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)


class Base(DeclarativeBase):
    """Base class for all database models."""
    pass


class DatabaseConfig:
    """Database configuration and connection management."""
    
    def __init__(self):
        self.database_url = os.getenv(
            "DATABASE_URL", 
            "postgresql+asyncpg://dataecho:secret@localhost:5432/dataecho"
        )
        self.engine = None
        self.async_session_maker = None
        
    async def initialize(self):
        """Initialize database connection and session maker."""
        try:
            self.engine = create_async_engine(
                self.database_url,
                echo=False,  # Set to True for SQL debugging
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
            )
            
            self.async_session_maker = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            logger.info("Database connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {str(e)}")
            raise
    
    async def create_tables(self):
        """Create all database tables."""
        try:
            async with self.engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {str(e)}")
            raise
    
    async def get_session(self) -> AsyncSession:
        """Get a database session."""
        if not self.async_session_maker:
            await self.initialize()
        return self.async_session_maker()
    
    async def close(self):
        """Close database connections."""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connections closed")


# Global database instance
db_config = DatabaseConfig()


async def get_db_session() -> AsyncSession:
    """Dependency for getting database session."""
    async with db_config.get_session() as session:
        try:
            yield session
        finally:
            await session.close()
