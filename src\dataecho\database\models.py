"""Database models for DataEcho."""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import Column, String, Integer, Float, DateTime, Text, JSON, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .config import Base


class Session(Base):
    """Session model for tracking data generation sessions."""
    
    __tablename__ = "sessions"
    
    id = Column(String, primary_key=True)
    domain = Column(String, nullable=False)
    user_context = Column(JSON, nullable=True)
    status = Column(String, default="created")
    current_step = Column(String, nullable=True)
    progress = Column(Integer, default=0)
    message = Column(Text, nullable=True)
    csv_file_path = Column(String, nullable=True)
    results_file_path = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    token_usage = relationship("TokenUsage", back_populates="session", cascade="all, delete-orphan")
    agent_calls = relationship("AgentCall", back_populates="session", cascade="all, delete-orphan")


class TokenUsage(Base):
    """Token usage tracking model."""
    
    __tablename__ = "token_usage"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String, ForeignKey("sessions.id"), nullable=False)
    agent_name = Column(String, nullable=False)
    model = Column(String, nullable=False)
    
    # Token counts
    prompt_tokens = Column(Integer, default=0)
    completion_tokens = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    
    # Cost tracking
    cost = Column(Float, default=0.0)
    
    # Timing
    duration_ms = Column(Integer, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    session = relationship("Session", back_populates="token_usage")


class AgentCall(Base):
    """Individual agent call tracking."""
    
    __tablename__ = "agent_calls"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String, ForeignKey("sessions.id"), nullable=False)
    agent_name = Column(String, nullable=False)
    model = Column(String, nullable=False)
    
    # Call details
    input_message = Column(Text, nullable=True)
    output_message = Column(Text, nullable=True)
    
    # Token usage for this specific call
    prompt_tokens = Column(Integer, default=0)
    completion_tokens = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    cost = Column(Float, default=0.0)
    
    # Performance metrics
    duration_ms = Column(Integer, nullable=True)
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    session = relationship("Session", back_populates="agent_calls")


class TokenAnalytics(Base):
    """Aggregated token usage analytics."""
    
    __tablename__ = "token_analytics"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(DateTime(timezone=True), nullable=False)
    
    # Daily aggregates
    total_tokens = Column(Integer, default=0)
    total_cost = Column(Float, default=0.0)
    total_requests = Column(Integer, default=0)
    
    # Agent breakdown (JSON field)
    agent_breakdown = Column(JSON, nullable=True)
    
    # Model breakdown (JSON field)
    model_breakdown = Column(JSON, nullable=True)
    
    # Performance metrics
    avg_duration_ms = Column(Float, nullable=True)
    success_rate = Column(Float, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
