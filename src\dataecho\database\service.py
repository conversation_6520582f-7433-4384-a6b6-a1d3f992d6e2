"""Database service for DataEcho operations."""

from datetime import datetime, date
from typing import Optional, Dict, Any, List
from sqlalchemy import select, func, and_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from .config import db_config
from .models import Session, <PERSON><PERSON><PERSON><PERSON>ge, AgentCall, TokenAnalytics
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)


class DatabaseService:
    """Service for database operations."""
    
    async def create_session(self, session_id: str, domain: str, user_context: Optional[Dict] = None) -> Session:
        """Create a new session in the database."""
        async with db_config.get_session() as db_session:
            try:
                session = Session(
                    id=session_id,
                    domain=domain,
                    user_context=user_context,
                    status="created"
                )
                db_session.add(session)
                await db_session.commit()
                await db_session.refresh(session)
                logger.info(f"Created session {session_id} in database")
                return session
            except Exception as e:
                await db_session.rollback()
                logger.error(f"Error creating session {session_id}: {str(e)}")
                raise
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session from the database."""
        async with db_config.get_session() as db_session:
            try:
                result = await db_session.execute(
                    select(Session).where(Session.id == session_id)
                )
                return result.scalar_one_or_none()
            except Exception as e:
                logger.error(f"Error getting session {session_id}: {str(e)}")
                return None
    
    async def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update a session in the database."""
        async with db_config.get_session() as db_session:
            try:
                result = await db_session.execute(
                    select(Session).where(Session.id == session_id)
                )
                session = result.scalar_one_or_none()
                
                if not session:
                    return False
                
                for key, value in updates.items():
                    if hasattr(session, key):
                        setattr(session, key, value)
                
                await db_session.commit()
                logger.info(f"Updated session {session_id}")
                return True
            except Exception as e:
                await db_session.rollback()
                logger.error(f"Error updating session {session_id}: {str(e)}")
                return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session and all related data."""
        async with db_config.get_session() as db_session:
            try:
                result = await db_session.execute(
                    select(Session).where(Session.id == session_id)
                )
                session = result.scalar_one_or_none()
                
                if not session:
                    return False
                
                await db_session.delete(session)
                await db_session.commit()
                logger.info(f"Deleted session {session_id}")
                return True
            except Exception as e:
                await db_session.rollback()
                logger.error(f"Error deleting session {session_id}: {str(e)}")
                return False
    
    async def record_token_usage(self, session_id: str, agent_name: str, model: str,
                               prompt_tokens: int, completion_tokens: int, cost: float,
                               duration_ms: Optional[int] = None) -> TokenUsage:
        """Record token usage for a session and agent."""
        async with db_config.get_session() as db_session:
            try:
                token_usage = TokenUsage(
                    session_id=session_id,
                    agent_name=agent_name,
                    model=model,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=prompt_tokens + completion_tokens,
                    cost=cost,
                    duration_ms=duration_ms
                )
                db_session.add(token_usage)
                await db_session.commit()
                await db_session.refresh(token_usage)
                return token_usage
            except Exception as e:
                await db_session.rollback()
                logger.error(f"Error recording token usage: {str(e)}")
                raise
    
    async def record_agent_call(self, session_id: str, agent_name: str, model: str,
                              input_message: str, output_message: str,
                              prompt_tokens: int, completion_tokens: int, cost: float,
                              duration_ms: Optional[int] = None, success: bool = True,
                              error_message: Optional[str] = None) -> AgentCall:
        """Record an individual agent call."""
        async with db_config.get_session() as db_session:
            try:
                agent_call = AgentCall(
                    session_id=session_id,
                    agent_name=agent_name,
                    model=model,
                    input_message=input_message,
                    output_message=output_message,
                    prompt_tokens=prompt_tokens,
                    completion_tokens=completion_tokens,
                    total_tokens=prompt_tokens + completion_tokens,
                    cost=cost,
                    duration_ms=duration_ms,
                    success=success,
                    error_message=error_message
                )
                db_session.add(agent_call)
                await db_session.commit()
                await db_session.refresh(agent_call)
                return agent_call
            except Exception as e:
                await db_session.rollback()
                logger.error(f"Error recording agent call: {str(e)}")
                raise
    
    async def get_session_token_usage(self, session_id: str) -> Dict[str, Any]:
        """Get token usage statistics for a session."""
        async with db_config.get_session() as db_session:
            try:
                # Get total usage for session
                result = await db_session.execute(
                    select(
                        func.sum(TokenUsage.total_tokens).label('total_tokens'),
                        func.sum(TokenUsage.cost).label('total_cost'),
                        func.count(TokenUsage.id).label('total_requests')
                    ).where(TokenUsage.session_id == session_id)
                )
                totals = result.first()
                
                # Get agent breakdown
                agent_result = await db_session.execute(
                    select(
                        TokenUsage.agent_name,
                        func.sum(TokenUsage.total_tokens).label('tokens'),
                        func.sum(TokenUsage.cost).label('cost'),
                        func.count(TokenUsage.id).label('requests')
                    ).where(TokenUsage.session_id == session_id)
                    .group_by(TokenUsage.agent_name)
                )
                
                agents = {}
                for row in agent_result:
                    agents[row.agent_name] = {
                        "tokens_used": row.tokens or 0,
                        "cost": row.cost or 0.0,
                        "requests": row.requests or 0
                    }
                
                return {
                    "session_id": session_id,
                    "total_tokens": totals.total_tokens or 0,
                    "total_cost": totals.total_cost or 0.0,
                    "total_requests": totals.total_requests or 0,
                    "agents": agents
                }
                
            except Exception as e:
                logger.error(f"Error getting session token usage: {str(e)}")
                return {
                    "session_id": session_id,
                    "total_tokens": 0,
                    "total_cost": 0.0,
                    "total_requests": 0,
                    "agents": {}
                }
    
    async def get_total_token_usage(self) -> Dict[str, Any]:
        """Get total token usage across all sessions."""
        async with db_config.get_session() as db_session:
            try:
                # Get overall totals
                result = await db_session.execute(
                    select(
                        func.sum(TokenUsage.total_tokens).label('total_tokens'),
                        func.sum(TokenUsage.cost).label('total_cost'),
                        func.count(TokenUsage.id).label('total_requests'),
                        func.count(func.distinct(TokenUsage.session_id)).label('session_count')
                    )
                )
                totals = result.first()
                
                # Get session breakdown
                session_result = await db_session.execute(
                    select(
                        TokenUsage.session_id,
                        func.sum(TokenUsage.total_tokens).label('tokens'),
                        func.sum(TokenUsage.cost).label('cost'),
                        func.min(TokenUsage.created_at).label('created_at'),
                        func.max(TokenUsage.created_at).label('updated_at')
                    ).group_by(TokenUsage.session_id)
                    .order_by(desc(func.max(TokenUsage.created_at)))
                )
                
                sessions = []
                for row in session_result:
                    sessions.append({
                        "session_id": row.session_id,
                        "tokens": row.tokens or 0,
                        "cost": row.cost or 0.0,
                        "created_at": row.created_at.isoformat() if row.created_at else None,
                        "updated_at": row.updated_at.isoformat() if row.updated_at else None
                    })
                
                return {
                    "total_tokens": totals.total_tokens or 0,
                    "total_cost": totals.total_cost or 0.0,
                    "total_requests": totals.total_requests or 0,
                    "session_count": totals.session_count or 0,
                    "sessions": sessions
                }
                
            except Exception as e:
                logger.error(f"Error getting total token usage: {str(e)}")
                return {
                    "total_tokens": 0,
                    "total_cost": 0.0,
                    "total_requests": 0,
                    "session_count": 0,
                    "sessions": []
                }


# Global database service instance
db_service = DatabaseService()
