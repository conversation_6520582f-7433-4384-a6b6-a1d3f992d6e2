

# File: app/models/agents.py
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from enum import Enum

class AgentType(str, Enum):
    PROFILER = "profiler"
    DEPENDENCY = "dependency"
    GENERATOR = "generator"
    RAG = "rag"  # For future RAG integration

class AgentStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class AgentConfig(BaseModel):
    model_config = {"protected_namespaces": ()}
    
    agent_type: AgentType
    model_name: str
    temperature: float = 1.0
    max_tokens: Optional[int] = None
    max_retries: int = 2

class AgentState(BaseModel):
    agent_id: str
    agent_type: AgentType
    status: AgentStatus
    session_id: str
    config: AgentConfig
    input_data: Dict[str, Any]
    output_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: str
    updated_at: str

