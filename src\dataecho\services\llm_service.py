# File: app/services/llm_service.py
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph
from langchain_core.messages import HumanMessage, SystemMessage
from dataecho.config import settings
from dataecho.utils.logger import setup_logger
from typing import Dict, Any, List, Optional
import json
import time
from datetime import datetime
from pathlib import Path

logger = setup_logger(__name__)

class LLMService:
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.default_model,
            temperature=settings.model_temperature,
            max_tokens=settings.model_max_tokens,
            timeout=settings.model_timeout,
            max_retries=settings.model_max_retries,
            base_url=settings.openrouter_base_url,
            api_key=settings.openrouter_api_key
        )
    
    def create_graph(self, memory: MemorySaver):
        """Create a LangGraph workflow"""
        workflow = StateGraph(state_schema=MessagesState)
        
        def call_model(state: MessagesState):
            response = self.llm.invoke(state["messages"])
            return {"messages": response}
        
        workflow.add_edge(START, "model")
        workflow.add_node("model", call_model)
        
        graph = workflow.compile(checkpointer=memory)
        return graph
    
    async def invoke_with_messages(self, messages: List[Any], config: Dict[str, Any], memory: MemorySaver):
        """Invoke LLM with messages using graph"""
        result = ""
        try:
            graph = self.create_graph(memory)
            # result = graph.invoke({"messages": messages}, config)
            result = await graph.ainvoke({"messages": messages}, config)

            return result
        except Exception as e:
            logger.error(f"Error invoking LLM: {str(e)}")
            logger.error(f" The model response was: \n {result}")
            raise
