# File: app/services/llm_service.py
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import START, MessagesState, StateGraph
from langchain_core.messages import HumanMessage, SystemMessage
from dataecho.config import settings
from dataecho.utils.logger import setup_logger
from typing import Dict, Any, List, Optional
import json
import time
from datetime import datetime
from pathlib import Path

logger = setup_logger(__name__)

class TokenUsageTracker:
    """Track token usage across sessions and agents"""

    def __init__(self):
        self.token_storage_dir = Path("token_usage")
        self.token_storage_dir.mkdir(exist_ok=True)

    def save_token_usage(self, session_id: str, agent_name: str, token_data: Dict[str, Any]):
        """Save token usage data to file"""
        try:
            usage_file = self.token_storage_dir / f"{session_id}_tokens.json"

            # Load existing data or create new
            if usage_file.exists():
                with open(usage_file, 'r') as f:
                    data = json.load(f)
            else:
                data = {
                    "session_id": session_id,
                    "total_tokens": 0,
                    "total_cost": 0.0,
                    "agents": {},
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat()
                }

            # Update agent-specific data
            if agent_name not in data["agents"]:
                data["agents"][agent_name] = {
                    "tokens_used": 0,
                    "cost": 0.0,
                    "requests": 0,
                    "calls": []
                }

            # Add this call's data
            call_data = {
                "timestamp": datetime.now().isoformat(),
                "tokens": token_data.get("total_tokens", 0),
                "prompt_tokens": token_data.get("prompt_tokens", 0),
                "completion_tokens": token_data.get("completion_tokens", 0),
                "cost": token_data.get("cost", 0.0),
                "model": token_data.get("model", settings.default_model)
            }

            data["agents"][agent_name]["calls"].append(call_data)
            data["agents"][agent_name]["tokens_used"] += call_data["tokens"]
            data["agents"][agent_name]["cost"] += call_data["cost"]
            data["agents"][agent_name]["requests"] += 1

            # Update totals
            data["total_tokens"] += call_data["tokens"]
            data["total_cost"] += call_data["cost"]
            data["updated_at"] = datetime.now().isoformat()

            # Save updated data
            with open(usage_file, 'w') as f:
                json.dump(data, f, indent=2)

            logger.info(f"Token usage saved for {agent_name}: {call_data['tokens']} tokens")

        except Exception as e:
            logger.error(f"Error saving token usage: {str(e)}")

    def get_session_token_usage(self, session_id: str) -> Dict[str, Any]:
        """Get token usage for a session"""
        try:
            usage_file = self.token_storage_dir / f"{session_id}_tokens.json"
            if usage_file.exists():
                with open(usage_file, 'r') as f:
                    return json.load(f)
            return {
                "session_id": session_id,
                "total_tokens": 0,
                "total_cost": 0.0,
                "agents": {}
            }
        except Exception as e:
            logger.error(f"Error loading token usage: {str(e)}")
            return {"session_id": session_id, "total_tokens": 0, "total_cost": 0.0, "agents": {}}


class LLMService:
    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.default_model,
            temperature=settings.model_temperature,
            max_tokens=settings.model_max_tokens,
            timeout=settings.model_timeout,
            max_retries=settings.model_max_retries,
            base_url=settings.openrouter_base_url,
            api_key=settings.openrouter_api_key
        )
        self.token_tracker = TokenUsageTracker()
    
    def create_graph(self, memory: MemorySaver, session_id: Optional[str] = None, agent_name: Optional[str] = None):
        """Create a LangGraph workflow with token tracking"""
        workflow = StateGraph(state_schema=MessagesState)

        def call_model(state: MessagesState):
            start_time = time.time()
            response = self.llm.invoke(state["messages"])
            end_time = time.time()

            # Extract token usage from response if available
            if hasattr(response, 'response_metadata') and response.response_metadata:
                usage = response.response_metadata.get('token_usage', {})
                if usage and session_id and agent_name:
                    # Calculate estimated cost (rough estimate for OpenRouter)
                    total_tokens = usage.get('total_tokens', 0)
                    estimated_cost = self._estimate_cost(total_tokens, settings.default_model)

                    token_data = {
                        "total_tokens": total_tokens,
                        "prompt_tokens": usage.get('prompt_tokens', 0),
                        "completion_tokens": usage.get('completion_tokens', 0),
                        "cost": estimated_cost,
                        "model": settings.default_model,
                        "duration": end_time - start_time
                    }

                    # Save token usage
                    self.token_tracker.save_token_usage(session_id, agent_name, token_data)

            return {"messages": response}

        workflow.add_edge(START, "model")
        workflow.add_node("model", call_model)

        graph = workflow.compile(checkpointer=memory)
        return graph
    
    async def invoke_with_messages(self, messages: List[Any], config: Dict[str, Any], memory: MemorySaver,
                                 session_id: Optional[str] = None, agent_name: Optional[str] = None):
        """Invoke LLM with messages using graph with token tracking"""
        result = ""
        try:
            graph = self.create_graph(memory, session_id, agent_name)
            result = await graph.ainvoke({"messages": messages}, config)
            return result
        except Exception as e:
            logger.error(f"Error invoking LLM: {str(e)}")
            logger.error(f" The model response was: \n {result}")
            raise

    def _estimate_cost(self, total_tokens: int, model: str) -> float:
        """Estimate cost based on token usage and model"""
        # Rough cost estimates per 1K tokens (these are approximations)
        cost_per_1k = {
            "deepseek/deepseek-prover-v2:free": 0.0,  # Free model
            "google/gemini-2.5-flash": 0.0001,  # Very cheap
            "openai/gpt-3.5-turbo": 0.002,
            "openai/gpt-4": 0.03,
            "openai/gpt-4-turbo": 0.01,
            "anthropic/claude-3-haiku": 0.00025,
            "anthropic/claude-3-sonnet": 0.003,
            "anthropic/claude-3-opus": 0.015
        }

        # Default cost if model not found
        rate = cost_per_1k.get(model, 0.001)
        return (total_tokens / 1000) * rate

    def get_session_tokens(self, session_id: str) -> Dict[str, Any]:
        """Get token usage for a session"""
        return self.token_tracker.get_session_token_usage(session_id)
