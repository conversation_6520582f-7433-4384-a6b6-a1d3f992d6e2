# File: app/services/session_manager.py
import uuid
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from dataecho.config import settings
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)

class SessionManager:
    def __init__(self):
        self.sessions_dir = settings.session_storage_dir
        os.makedirs(self.sessions_dir, exist_ok=True)
    
    async def create_session(self, domain: str, user_context: str = "") -> str:
        """Create a new session"""
        session_id = str(uuid.uuid4())
        session_data = {
            "session_id": session_id,
            "domain": domain,
            "user_context": user_context,
            "status": "created",
            "current_step": "",
            "progress": 0,
            "message": "Session created",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "data": {}
        }
        
        await self._save_session(session_id, session_data)
        logger.info(f"Created session: {session_id}")
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        try:
            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
            if os.path.exists(session_file):
                with open(session_file, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {str(e)}")
            return None
    
    async def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update session data"""
        try:
            session = await self.get_session(session_id)
            if not session:
                return False
            
            session.update(updates)
            session["updated_at"] = datetime.utcnow().isoformat()
            
            await self._save_session(session_id, session)
            return True
        except Exception as e:
            logger.error(f"Error updating session {session_id}: {str(e)}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        try:
            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
            if os.path.exists(session_file):
                os.remove(session_file)
            logger.info(f"Deleted session: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {str(e)}")
            return False
    
    async def _save_session(self, session_id: str, session_data: Dict[str, Any]):
        """Save session data to file"""
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=2)

