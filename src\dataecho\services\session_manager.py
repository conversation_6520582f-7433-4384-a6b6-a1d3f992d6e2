# File: app/services/session_manager.py
import uuid
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
from dataecho.config import settings
from dataecho.database.service import db_service
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)

class SessionManager:
    def __init__(self):
        # Keep file-based storage as fallback for now
        self.sessions_dir = settings.session_storage_dir
        os.makedirs(self.sessions_dir, exist_ok=True)
    
    async def create_session(self, domain: str, user_context: str = "") -> str:
        """Create a new session"""
        session_id = str(uuid.uuid4())

        # Parse user_context if it's a JSON string, otherwise create a dict
        context_dict = None
        if user_context:
            try:
                context_dict = json.loads(user_context) if isinstance(user_context, str) else user_context
            except json.JSONDecodeError:
                context_dict = {"raw_context": user_context}

        # Create session in database
        session_data = await db_service.create_session(
            session_id=session_id,
            domain=domain,
            user_context=context_dict
        )

        # Also save to file system as backup
        session_file_data = {
            "session_id": session_id,
            "domain": domain,
            "user_context": user_context,
            "status": "created",
            "current_step": "",
            "progress": 0,
            "message": "Session created",
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
            "data": {}
        }
        await self._save_session(session_id, session_file_data)

        logger.info(f"Created session: {session_id}")
        return session_id
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data"""
        try:
            # Try to get from database first
            session_obj = await db_service.get_session(session_id)
            if session_obj:
                return self._session_to_dict(session_obj)

            # Fallback to file system
            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
            if os.path.exists(session_file):
                with open(session_file, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logger.error(f"Error getting session {session_id}: {str(e)}")
            return None

    def _session_to_dict(self, session) -> Dict[str, Any]:
        """Convert Session object to dictionary format expected by frontend."""
        return {
            "session_id": session.id,
            "domain": session.domain,
            "user_context": json.dumps(session.user_context) if session.user_context else "",
            "status": session.status,
            "current_step": session.current_step or "",
            "progress": session.progress or 0,
            "message": session.message or "",
            "created_at": session.created_at.isoformat() if session.created_at else "",
            "updated_at": session.updated_at.isoformat() if session.updated_at else "",
            "csv_file_path": session.csv_file_path,
            "results_file_path": session.results_file_path,
            "data": {}  # Additional data field for compatibility
        }
    
    async def update_session(self, session_id: str, updates: Dict[str, Any]) -> bool:
        """Update session data"""
        try:
            # Update in database
            success = await db_service.update_session(session_id, updates)
            if not success:
                return False

            # Also update file system backup
            session = await self.get_session(session_id)
            if session:
                session.update(updates)
                session["updated_at"] = datetime.utcnow().isoformat()
                await self._save_session(session_id, session)

            return True
        except Exception as e:
            logger.error(f"Error updating session {session_id}: {str(e)}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        try:
            session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
            if os.path.exists(session_file):
                os.remove(session_file)
            logger.info(f"Deleted session: {session_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {str(e)}")
            return False
    
    async def _save_session(self, session_id: str, session_data: Dict[str, Any]):
        """Save session data to file"""
        session_file = os.path.join(self.sessions_dir, f"{session_id}.json")
        with open(session_file, 'w') as f:
            json.dump(session_data, f, indent=2)

