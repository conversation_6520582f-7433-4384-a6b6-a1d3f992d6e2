
# File: app/storage/storage_manager.py
import os
import json
import aiofiles
from datetime import datetime
from typing import Dict, Any, Optional, List
from fastapi import UploadFile
from dataecho.config import settings
from dataecho.utils.logger import setup_logger

logger = setup_logger(__name__)

class StorageManager:
    """Abstract storage manager that can work with files now and MongoDB later"""
    
    def __init__(self):
        self.upload_dir = settings.upload_dir
        self.session_dir = settings.session_storage_dir
        self.results_dir = "results"
        
        # Create directories
        for directory in [self.upload_dir, self.session_dir, self.results_dir]:
            os.makedirs(directory, exist_ok=True)
    
    async def initialize(self):
        """Initialize storage (prepare for future MongoDB integration)"""
        logger.info("Storage manager initialized with file system")
        # Future: Initialize MongoDB connection
    
    async def save_uploaded_file(self, session_id: str, file: UploadFile) -> str:
        """Save uploaded file"""
        try:
            filename = f"{session_id}_{file.filename}"
            file_path = os.path.join(self.upload_dir, filename)
            
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            logger.info(f"Saved uploaded file: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Error saving uploaded file: {str(e)}")
            raise
    
    async def save_agent_result(self, session_id: str, agent_type: str, result: Dict[str, Any]):
        """Save agent result"""
        try:
            result_file = os.path.join(self.results_dir, f"{session_id}_{agent_type}.json")
            
            async with aiofiles.open(result_file, 'w') as f:
                await f.write(json.dumps(result, indent=2))
            
            logger.info(f"Saved {agent_type} result for session {session_id}")
        except Exception as e:
            logger.error(f"Error saving agent result: {str(e)}")
            raise
    
    async def get_agent_result(self, session_id: str, agent_type: str) -> Optional[Dict[str, Any]]:
        """Get agent result"""
        try:
            result_file = os.path.join(self.results_dir, f"{session_id}_{agent_type}.json")
            
            if os.path.exists(result_file):
                async with aiofiles.open(result_file, 'r') as f:
                    content = await f.read()
                    return json.loads(content)
            return None
        except Exception as e:
            logger.error(f"Error getting agent result: {str(e)}")
            return None
    
    async def get_session_results(self, session_id: str) -> Dict[str, Any]:
        """Get all results for a session"""
        try:
            results = {}
            
            # Get all result files for this session
            for filename in os.listdir(self.results_dir):
                if filename.startswith(f"{session_id}_") and filename.endswith('.json'):
                    agent_type = filename.replace(f"{session_id}_", "").replace(".json", "")
                    result = await self.get_agent_result(session_id, agent_type)
                    if result:
                        results[agent_type] = result
            
            return results
        except Exception as e:
            logger.error(f"Error getting session results: {str(e)}")
            return {}
    
    async def cleanup_session_data(self, session_id: str):
        """Clean up all data for a session"""
        try:
            # Remove uploaded files
            for filename in os.listdir(self.upload_dir):
                if filename.startswith(session_id):
                    os.remove(os.path.join(self.upload_dir, filename))
            
            # Remove result files
            for filename in os.listdir(self.results_dir):
                if filename.startswith(session_id):
                    os.remove(os.path.join(self.results_dir, filename))
            
            logger.info(f"Cleaned up session data for: {session_id}")
        except Exception as e:
            logger.error(f"Error cleaning up session data: {str(e)}")

