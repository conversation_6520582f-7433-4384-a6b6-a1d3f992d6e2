# File: app/utils/data_utils.py
import json
import re
from typing import Dict, Any, Optional

def extract_and_parse_json(content: str) -> Optional[Dict[str, Any]]:
    """Extract and parse JSON from LLM response"""
    try:
        # Try to find JSON block in the content
        json_match = re.search(r'```json\s*(.*?)\s*```', content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to find JSON object directly
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
            else:
                # Assume the entire content is JSON
                json_str = content
        
        return json.loads(json_str)
    except json.JSONDecodeError as e:
        try:
            return extract_heuristic_json(content)
        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {e}")
            print(f"Content: {content}")
            return None
    except Exception as e:
        print(f"Unexpected error in JSON extraction: {e}")
        return None



import ast

def extract_heuristic_json(content: str) -> Optional[Dict[str, Any]]:
    """
    Heuristic JSON parser to extract as much valid structure as possible from malformed LLM output.
    Returns a partial dict or list if possible.
    """
    try:
        # Remove markdown/code formatting
        content = content.strip()
        content = re.sub(r"^```(json)?", "", content)
        content = re.sub(r"```$", "", content)
        
        # Try fast path first
        result = json.loads(content)
        return result
    except json.JSONDecodeError:
        pass  # Try fallback

    # If it's a list of objects, extract valid objects one by one
    try:
        list_match = re.findall(r"\{.*?\}", content, re.DOTALL)
        parsed_items = []
        for item in list_match:
            try:
                parsed_item = json.loads(item)
                parsed_items.append(parsed_item)
            except json.JSONDecodeError:
                continue  # skip malformed entries
        if parsed_items:
            return parsed_items
    except Exception as e:
        print(f"Heuristic list recovery failed: {e}")

    # Try using ast.literal_eval as a last resort (safer eval)
    try:
        cleaned = re.sub(r"[^{}\[\],:\"\d\w\s.\-+*/'=]", "", content)
        result = ast.literal_eval(cleaned)
        if isinstance(result, (dict, list)):
            return result
    except (ValueError, SyntaxError):
        # AST parsing failed silently - this is expected for malformed content
        pass
    except Exception as e:
        print(f"AST fallback failed with unexpected error: {e}")

    return None



def save_file(filepath: str, data: Any):
    """Save data to file"""
    try:
        with open(filepath, 'w') as f:
            if isinstance(data, (dict, list)):
                json.dump(data, f, indent=2)
            else:
                f.write(str(data))
    except Exception as e:
        print(f"Error saving file {filepath}: {e}")

