#!/bin/bash

# --- CONFIGURATION ---
USERNAME="skm64060"
IMAGE="dataecho"
TAG="v1"
IMAGE_NAME="$USERNAME/$IMAGE:$TAG"
CONTAINER_NAME="dataecho_app"
DOCKERHUB_PAT="************************************"  # You can export this before running the script instead of hardcoding

# --- LOGIN TO DOCKER HUB USING PAT ---
echo "🔐 Logging into DockerHub with PAT..."
echo "$DOCKERHUB_PAT" | docker login --username "$USERNAME" --password-stdin

if [ $? -ne 0 ]; then
    echo "❌ Docker login failed. Please check your PAT."
    exit 1
fi

# --- PULL IMAGE IF NOT PRESENT ---
if [[ "$(docker images -q $IMAGE_NAME 2> /dev/null)" == "" ]]; then
  echo "📥 Pulling image $IMAGE_NAME..."
  docker pull $IMAGE_NAME
else
  echo "✅ Image $IMAGE_NAME already exists locally."
fi

# --- STOP & REMOVE OLD CONTAINER IF EXISTS ---
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
    echo "🧹 Removing old container $CONTAINER_NAME..."
    docker rm -f $CONTAINER_NAME
fi

# --- RUN CONTAINER WITH .env FILE ---
echo "🚀 Running container $CONTAINER_NAME..."
docker run -d \
  --name $CONTAINER_NAME \
  --env-file .env \
  -p 8000:8000 \
  $IMAGE_NAME

echo "✅ Container $CONTAINER_NAME is running. Access FastAPI at http://localhost:8000"
