#!/usr/bin/env python3

import asyncio
import aiohttp
import json

async def test_api_endpoints():
    """Test the API endpoints for session creation and file upload."""
    
    base_url = "http://localhost:8001"
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test 1: Create session
            print("🧪 Testing session creation...")
            session_data = {
                "domain": "financial services",
                "user_context": "Generate trading data for portfolio analysis"
            }
            
            async with session.post(
                f"{base_url}/api/v1/sessions",
                json=session_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    session_id = result["session_id"]
                    print(f"✅ Session created: {session_id}")
                else:
                    print(f"❌ Session creation failed: {response.status}")
                    text = await response.text()
                    print(f"   Response: {text}")
                    return
            
            # Test 2: Get session status
            print("🧪 Testing session status...")
            async with session.get(f"{base_url}/api/v1/sessions/{session_id}/status") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Session status: {result['status']}")
                else:
                    print(f"❌ Session status failed: {response.status}")
            
            # Test 3: File upload simulation
            print("🧪 Testing file upload...")
            
            # Create test CSV content
            csv_content = "name,age,salary\nJohn,30,50000\nJane,25,60000\nBob,35,70000"
            
            # Create form data for file upload
            data = aiohttp.FormData()
            data.add_field('file', csv_content, filename='test.csv', content_type='text/csv')
            
            async with session.post(
                f"{base_url}/api/v1/sessions/{session_id}/upload-csv",
                data=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ File uploaded: {result['message']}")
                    print(f"   File path: {result.get('file_path', 'N/A')}")
                else:
                    print(f"❌ File upload failed: {response.status}")
                    text = await response.text()
                    print(f"   Response: {text}")
            
            # Test 4: Check session after file upload
            print("🧪 Testing session status after upload...")
            async with session.get(f"{base_url}/api/v1/sessions/{session_id}/status") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Updated session status: {result['status']}")
                    print(f"   CSV file path: {result.get('data', {}).get('csv_file_path', 'N/A')}")
                else:
                    print(f"❌ Session status check failed: {response.status}")
                    
        except Exception as e:
            print(f"❌ Error during API testing: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_api_endpoints())
