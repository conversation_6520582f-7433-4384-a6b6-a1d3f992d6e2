#!/usr/bin/env python3
"""Simple test to check database connection."""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from dataecho.database.config import db_config

async def test_connection():
    """Test basic database connection."""
    print("Testing database connection...")
    
    try:
        # Initialize the database connection
        await db_config.initialize()
        print("✅ Database connection initialized")
        
        # Try to get a session
        session = db_config.get_session()
        async with session as db_session:
            print("✅ Database session created successfully")

            # Try a simple query
            from sqlalchemy import text
            result = await db_session.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            print(f"✅ Simple query executed: {row}")
        
        print("✅ All database connection tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await db_config.close()

if __name__ == "__main__":
    success = asyncio.run(test_connection())
    sys.exit(0 if success else 1)
