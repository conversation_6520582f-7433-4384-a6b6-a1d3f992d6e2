#!/usr/bin/env python3
"""Test script to verify database integration works correctly."""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from dataecho.database.service import db_service

async def test_database_integration():
    """Test the database integration."""
    print("Testing database integration...")
    
    try:
        # Test 1: Get total token usage (should return empty data initially)
        print("\n1. Testing get_total_token_usage...")
        total_usage = await db_service.get_total_token_usage()
        print(f"Total usage: {total_usage}")
        
        # Test 2: Get session token usage for non-existent session
        print("\n2. Testing get_session_token_usage for non-existent session...")
        session_usage = await db_service.get_session_token_usage("test-session-123")
        print(f"Session usage: {session_usage}")
        
        # Test 3: Record some token usage
        print("\n3. Testing record_token_usage...")
        await db_service.record_token_usage(
            session_id="test-session-123",
            agent_name="TestAgent",
            model="test-model",
            prompt_tokens=100,
            completion_tokens=50,
            cost=0.01,
            duration_ms=1500
        )
        print("Token usage recorded successfully")
        
        # Test 4: Get session token usage again
        print("\n4. Testing get_session_token_usage after recording...")
        session_usage = await db_service.get_session_token_usage("test-session-123")
        print(f"Session usage after recording: {session_usage}")
        
        # Test 5: Get token analytics
        print("\n5. Testing get_token_analytics...")
        analytics = await db_service.get_token_analytics()
        print(f"Analytics: {analytics}")
        
        print("\n✅ All database integration tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database integration test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_database_integration())
    sys.exit(0 if success else 1)
