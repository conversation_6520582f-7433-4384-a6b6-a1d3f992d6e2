#!/usr/bin/env python3

import asyncio
import aiohttp
import json
import time

async def test_end_to_end_flow():
    """Test the complete end-to-end data generation flow."""
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        try:
            print("🚀 Starting End-to-End DataEcho Test")
            print("=" * 50)
            
            # Step 1: Create session
            print("📝 Step 1: Creating session...")
            session_data = {
                "domain": "financial services",
                "user_context": "Generate trading data for portfolio analysis with realistic market patterns"
            }
            
            async with session.post(
                f"{base_url}/api/v1/sessions",
                json=session_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    session_id = result["session_id"]
                    print(f"✅ Session created: {session_id}")
                else:
                    print(f"❌ Session creation failed: {response.status}")
                    return
            
            # Step 2: Upload CSV file
            print("📁 Step 2: Uploading CSV file...")
            csv_content = """symbol,price,volume,timestamp
AAPL,150.25,1000000,2024-01-01T09:30:00
GOOGL,2800.50,500000,2024-01-01T09:30:00
MSFT,380.75,750000,2024-01-01T09:30:00
TSLA,220.30,2000000,2024-01-01T09:30:00
AMZN,3200.80,300000,2024-01-01T09:30:00"""
            
            data = aiohttp.FormData()
            data.add_field('file', csv_content, filename='trading_data.csv', content_type='text/csv')
            
            async with session.post(
                f"{base_url}/api/v1/sessions/{session_id}/upload-csv",
                data=data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ File uploaded: {result['message']}")
                    print(f"   📂 File path: {result.get('file_path', 'N/A')}")
                else:
                    print(f"❌ File upload failed: {response.status}")
                    return
            
            # Step 3: Check session status after upload
            print("🔍 Step 3: Verifying session status...")
            async with session.get(f"{base_url}/api/v1/sessions/{session_id}/status") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Session status: {result['status']}")
                    print(f"   📊 CSV file: {result.get('data', {}).get('csv_file_path', 'N/A')}")
                else:
                    print(f"❌ Session status check failed: {response.status}")
                    return
            
            # Step 4: Start data generation
            print("🤖 Step 4: Starting data generation...")
            generation_request = {
                "n_rows": 10,
                "batch_size": 5
            }
            
            async with session.post(
                f"{base_url}/api/v1/sessions/{session_id}/generate",
                json=generation_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Data generation started: {result['message']}")
                else:
                    print(f"❌ Data generation failed: {response.status}")
                    text = await response.text()
                    print(f"   Response: {text}")
                    return
            
            # Step 5: Monitor progress
            print("⏳ Step 5: Monitoring progress...")
            max_wait_time = 120  # 2 minutes max
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                async with session.get(f"{base_url}/api/v1/sessions/{session_id}/status") as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result['status']
                        progress = result['progress']
                        current_step = result['current_step']
                        message = result['message']
                        
                        print(f"   📈 Status: {status} | Step: {current_step} | Progress: {progress}% | {message}")
                        
                        if status == "completed":
                            print("🎉 Data generation completed successfully!")
                            break
                        elif status == "failed":
                            print("❌ Data generation failed!")
                            break
                    
                    await asyncio.sleep(5)  # Check every 5 seconds
            else:
                print("⏰ Timeout waiting for completion")
            
            # Step 6: Get results
            print("📊 Step 6: Retrieving results...")
            async with session.get(f"{base_url}/api/v1/sessions/{session_id}/results") as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✅ Results retrieved:")
                    print(f"   📁 Results file: {result.get('results_file_path', 'N/A')}")
                    if 'data' in result:
                        print(f"   📊 Generated rows: {len(result.get('data', []))}")
                else:
                    print(f"❌ Failed to retrieve results: {response.status}")
            
            print("=" * 50)
            print("🏁 End-to-End Test Complete!")
            
        except Exception as e:
            print(f"❌ Error during end-to-end testing: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_end_to_end_flow())
