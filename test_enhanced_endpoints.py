#!/usr/bin/env python3

import requests
import json
import time

def test_enhanced_endpoints():
    """Test the enhanced results endpoints."""
    
    base_url = "http://localhost:8000"
    
    try:
        print("🚀 Testing Enhanced DataEcho Endpoints")
        print("=" * 50)
        
        # Step 1: Create session and run generation
        print("📝 Creating session and running generation...")
        session_data = {
            "domain": "financial trading",
            "user_context": "Stock trading data with prices and volumes"
        }
        
        response = requests.post(
            f"{base_url}/api/v1/sessions",
            json=session_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            session_id = result["session_id"]
            print(f"✅ Session created: {session_id}")
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            return
        
        # Step 2: Upload file
        print("📁 Uploading CSV file...")
        csv_content = "symbol,price,volume\nAAPL,150.25,1000000\nGOOGL,2800.50,500000\nTSLA,800.75,750000"
        files = {'file': ('trading_data.csv', csv_content, 'text/csv')}
        
        response = requests.post(
            f"{base_url}/api/v1/sessions/{session_id}/upload-csv",
            files=files
        )
        
        if response.status_code == 200:
            print("✅ File uploaded successfully")
        else:
            print(f"❌ File upload failed: {response.status_code}")
            return
        
        # Step 3: Start generation
        print("🤖 Starting data generation...")
        generation_data = {
            "n_rows": 10,
            "batch_size": 5
        }
        
        response = requests.post(
            f"{base_url}/api/v1/sessions/{session_id}/generate",
            json=generation_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print("✅ Generation started")
        else:
            print(f"❌ Generation failed to start: {response.status_code}")
            return
        
        # Step 4: Wait for completion
        print("⏳ Waiting for generation to complete...")
        max_wait = 60  # 60 seconds max
        wait_time = 0
        
        while wait_time < max_wait:
            response = requests.get(f"{base_url}/api/v1/sessions/{session_id}/status")
            if response.status_code == 200:
                status_data = response.json()
                status = status_data.get("status", "unknown")
                progress = status_data.get("progress", 0)
                
                print(f"   Status: {status} | Progress: {progress}%")
                
                if status == "completed":
                    print("✅ Generation completed!")
                    break
                elif status == "failed":
                    print("❌ Generation failed!")
                    return
            
            time.sleep(2)
            wait_time += 2
        
        if wait_time >= max_wait:
            print("⏰ Timeout waiting for completion")
            return
        
        # Step 5: Test enhanced results endpoint
        print("\n📊 Testing Enhanced Results Endpoint...")
        response = requests.get(f"{base_url}/api/v1/sessions/{session_id}/results")
        
        if response.status_code == 200:
            results = response.json()
            print("✅ Enhanced results retrieved:")
            print(f"   📈 Status: {results.get('status')}")
            print(f"   📊 Preview rows: {len(results.get('preview_data', []))}")
            print(f"   🎯 Quality metrics: {bool(results.get('quality_metrics'))}")
            print(f"   📋 Generation summary: {bool(results.get('generation_summary'))}")
        else:
            print(f"❌ Enhanced results failed: {response.status_code}")
        
        # Step 6: Test data preview endpoint
        print("\n🔍 Testing Data Preview Endpoint...")
        response = requests.get(f"{base_url}/api/v1/sessions/{session_id}/preview?limit=5")
        
        if response.status_code == 200:
            preview = response.json()
            print("✅ Data preview retrieved:")
            print(f"   📊 Total rows: {preview.get('total_rows')}")
            print(f"   👀 Preview rows: {preview.get('preview_rows')}")
            print(f"   📋 Columns: {preview.get('columns')}")
        else:
            print(f"❌ Data preview failed: {response.status_code}")
        
        # Step 7: Test quality metrics endpoint
        print("\n🎯 Testing Quality Metrics Endpoint...")
        response = requests.get(f"{base_url}/api/v1/sessions/{session_id}/quality")
        
        if response.status_code == 200:
            quality = response.json()
            print("✅ Quality metrics retrieved:")
            print(f"   🎯 Overall quality: {quality.get('overall_quality')}")
            print(f"   📊 Data consistency: {quality.get('data_consistency')}")
            print(f"   ⏱️  Generation time: {quality.get('generation_time')}")
            print(f"   📈 Total rows: {quality.get('total_rows')}")
        else:
            print(f"❌ Quality metrics failed: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("🏁 Enhanced Endpoints Test Complete!")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_endpoints()
