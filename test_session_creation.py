#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

from src.dataecho.services.session_manager import SessionManager

async def test_session_creation():
    """Test session creation with the updated session manager."""
    try:
        session_manager = SessionManager()
        
        print("Creating session...")
        session_id = await session_manager.create_session(
            domain="test_domain",
            user_context="test context"
        )
        print(f"✅ Session created successfully: {session_id}")
        
        print("Retrieving session...")
        session_data = await session_manager.get_session(session_id)
        if session_data:
            print(f"✅ Session retrieved successfully:")
            print(f"   - ID: {session_data['session_id']}")
            print(f"   - Domain: {session_data['domain']}")
            print(f"   - Status: {session_data['status']}")
            print(f"   - User Context: {session_data['user_context']}")
        else:
            print("❌ Failed to retrieve session")
            
        print("Updating session...")
        success = await session_manager.update_session(session_id, {
            "csv_file_path": "/test/path/file.csv",
            "status": "file_uploaded"
        })
        if success:
            print("✅ Session updated successfully")
            
            # Retrieve updated session
            updated_session = await session_manager.get_session(session_id)
            if updated_session:
                print(f"   - CSV File Path: {updated_session.get('csv_file_path')}")
                print(f"   - Status: {updated_session['status']}")
        else:
            print("❌ Failed to update session")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_session_creation())
