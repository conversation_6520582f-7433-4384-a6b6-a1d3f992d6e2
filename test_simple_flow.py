#!/usr/bin/env python3

import requests
import json
import time

def test_simple_flow():
    """Test the basic flow with requests library."""
    
    base_url = "http://localhost:8001"
    
    try:
        print("🚀 Starting Simple DataEcho Test")
        print("=" * 40)
        
        # Step 1: Create session
        print("📝 Creating session...")
        session_data = {
            "domain": "test",
            "user_context": "test context"
        }
        
        response = requests.post(
            f"{base_url}/api/v1/sessions",
            json=session_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            session_id = result["session_id"]
            print(f"✅ Session created: {session_id}")
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            print(f"Response: {response.text}")
            return
        
        # Step 2: Check session status
        print("🔍 Checking session status...")
        response = requests.get(f"{base_url}/api/v1/sessions/{session_id}/status")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Session status: {result['status']}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return
        
        # Step 3: Upload file
        print("📁 Uploading CSV file...")
        csv_content = "name,age\nJohn,30\nJane,25"
        files = {'file': ('test.csv', csv_content, 'text/csv')}
        
        response = requests.post(
            f"{base_url}/api/v1/sessions/{session_id}/upload-csv",
            files=files
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ File uploaded: {result['message']}")
        else:
            print(f"❌ File upload failed: {response.status_code}")
            print(f"Response: {response.text}")
            return
        
        # Step 4: Check session after upload
        print("🔍 Checking session after upload...")
        response = requests.get(f"{base_url}/api/v1/sessions/{session_id}/status")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Session status: {result['status']}")
            csv_path = result.get('data', {}).get('csv_file_path')
            if csv_path:
                print(f"   📂 CSV file: {csv_path}")
            else:
                print("   ⚠️  No CSV file path found")
        else:
            print(f"❌ Status check failed: {response.status_code}")
        
        print("=" * 40)
        print("🏁 Simple Test Complete!")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_flow()
