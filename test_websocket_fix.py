#!/usr/bin/env python3
"""Test script to verify WebSocket and async generation fixes."""

import asyncio
import json
import requests
import websockets
import time
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000"

async def test_websocket_and_async_generation():
    """Test the WebSocket connection and async generation workflow."""
    print("🚀 Testing WebSocket and Async Generation Fixes")
    print("=" * 60)
    
    try:
        # Step 1: Create session
        print("📝 Creating session...")
        response = requests.post(f"{BASE_URL}/api/v1/sessions", json={
            "domain": "e-commerce",
            "user_context": "Generate realistic product data for testing"
        })
        
        if response.status_code != 200:
            print(f"❌ Failed to create session: {response.status_code}")
            print(response.text)
            return
            
        session_data = response.json()
        session_id = session_data["session_id"]
        print(f"✅ Session created: {session_id}")
        
        # Step 2: Upload CSV file
        print("📁 Uploading CSV file...")
        csv_content = """product_name,price,category,rating
iPhone 14,999.99,Electronics,4.5
MacBook Pro,2499.99,Electronics,4.8
Nike Air Max,129.99,Footwear,4.2"""
        
        files = {"file": ("test_data.csv", csv_content, "text/csv")}
        response = requests.post(f"{BASE_URL}/api/v1/sessions/{session_id}/upload", files=files)
        
        if response.status_code != 200:
            print(f"❌ Failed to upload file: {response.status_code}")
            print(response.text)
            return
            
        print("✅ File uploaded successfully")
        
        # Step 3: Connect WebSocket BEFORE starting generation
        print("🔌 Connecting WebSocket...")
        websocket_messages = []
        
        async def websocket_listener():
            try:
                async with websockets.connect(f"{WS_URL}/ws/{session_id}") as websocket:
                    print("✅ WebSocket connected")
                    async for message in websocket:
                        data = json.loads(message)
                        websocket_messages.append(data)
                        print(f"📨 WebSocket message: {data.get('type', 'unknown')} - {data.get('data', {}).get('message', 'No message')}")
                        
                        # Break if we get completion or error
                        if data.get('type') in ['completion', 'error']:
                            break
                            
            except Exception as e:
                print(f"❌ WebSocket error: {str(e)}")
        
        # Start WebSocket listener in background
        websocket_task = asyncio.create_task(websocket_listener())
        
        # Give WebSocket time to connect
        await asyncio.sleep(1)
        
        # Step 4: Start generation (should return immediately)
        print("🤖 Starting data generation...")
        start_time = time.time()
        
        response = requests.post(f"{BASE_URL}/api/v1/sessions/{session_id}/generate", json={
            "n_rows": 5,
            "batch_size": 5
        })
        
        end_time = time.time()
        response_time = end_time - start_time
        
        print(f"⏱️  Generation endpoint response time: {response_time:.2f} seconds")
        
        if response.status_code != 200:
            print(f"❌ Failed to start generation: {response.status_code}")
            print(response.text)
            return
            
        result = response.json()
        print(f"✅ Generation started: {result['message']}")
        
        # Check if response was immediate (should be < 2 seconds)
        if response_time < 2:
            print("✅ Generation endpoint returned immediately (non-blocking)")
        else:
            print("❌ Generation endpoint took too long (still blocking)")
        
        # Step 5: Wait for WebSocket messages and completion
        print("⏳ Waiting for WebSocket messages...")
        
        try:
            await asyncio.wait_for(websocket_task, timeout=120)  # 2 minute timeout
        except asyncio.TimeoutError:
            print("❌ WebSocket timeout - no completion message received")
        
        # Step 6: Check final results
        print("\n📊 WebSocket Messages Summary:")
        print(f"Total messages received: {len(websocket_messages)}")
        
        message_types = {}
        for msg in websocket_messages:
            msg_type = msg.get('type', 'unknown')
            message_types[msg_type] = message_types.get(msg_type, 0) + 1
        
        for msg_type, count in message_types.items():
            print(f"  - {msg_type}: {count}")
        
        # Check if we got the expected message types
        expected_types = ['status_update', 'progress', 'completion']
        missing_types = [t for t in expected_types if t not in message_types]
        
        if not missing_types:
            print("✅ All expected WebSocket message types received")
        else:
            print(f"❌ Missing WebSocket message types: {missing_types}")
        
        # Step 7: Check final session status
        print("\n🔍 Checking final session status...")
        response = requests.get(f"{BASE_URL}/api/v1/sessions/{session_id}")
        
        if response.status_code == 200:
            session_data = response.json()
            print(f"Final status: {session_data.get('status', 'unknown')}")
            print(f"Progress: {session_data.get('progress', 0)}%")
            print(f"Message: {session_data.get('message', 'No message')}")
            
            if session_data.get('status') == 'completed':
                print("✅ Session completed successfully")
            else:
                print("❌ Session did not complete")
        else:
            print(f"❌ Failed to get session status: {response.status_code}")
        
        print("\n" + "=" * 60)
        print("🏁 Test Complete!")
        
        # Summary
        if response_time < 2 and len(websocket_messages) > 0 and not missing_types:
            print("🎉 SUCCESS: WebSocket and async generation fixes are working!")
        else:
            print("❌ ISSUES DETECTED: Some fixes may not be working properly")
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_websocket_and_async_generation())
