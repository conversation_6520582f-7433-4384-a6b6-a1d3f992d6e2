"""Pytest configuration and shared fixtures."""

import asyncio
import pytest
import tempfile
import shutil
from pathlib import Path
from typing import Generator, AsyncGenerator
from unittest.mock import AsyncMock, MagicMock

from fastapi.testclient import TestClient
from httpx import AsyncClient

from dataecho.services.session_manager import SessionManager
from dataecho.storage.storage_manager import StorageManager
from dataecho.websocket.connection_manager import ConnectionManager
from backend.api_service.main import create_app


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for tests."""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture
def mock_session_manager() -> AsyncMock:
    """Mock session manager for testing."""
    mock = AsyncMock(spec=SessionManager)
    mock.create_session.return_value = "test-session-id"
    mock.get_session.return_value = {
        "session_id": "test-session-id",
        "domain": "test-domain",
        "user_context": "test-context",
        "status": "created",
        "csv_file_path": "/test/path/file.csv"
    }
    return mock


@pytest.fixture
def mock_storage_manager(temp_dir: Path) -> AsyncMock:
    """Mock storage manager for testing."""
    mock = AsyncMock(spec=StorageManager)
    mock.results_dir = str(temp_dir)
    mock.sessions_dir = str(temp_dir)
    mock.uploads_dir = str(temp_dir)
    mock.save_uploaded_file.return_value = str(temp_dir / "test_file.csv")
    mock.get_session_results.return_value = {"test": "results"}
    return mock


@pytest.fixture
def mock_connection_manager() -> AsyncMock:
    """Mock WebSocket connection manager for testing."""
    mock = AsyncMock(spec=ConnectionManager)
    return mock


@pytest.fixture
def mock_llm_service() -> MagicMock:
    """Mock LLM service for testing."""
    mock = MagicMock()
    mock.generate_completion.return_value = "Mock LLM response"
    return mock


@pytest.fixture
def sample_csv_data() -> str:
    """Sample CSV data for testing."""
    return """name,age,city,salary
John Doe,30,New York,50000
Jane Smith,25,Los Angeles,60000
Bob Johnson,35,Chicago,55000
Alice Brown,28,Houston,52000"""


@pytest.fixture
def sample_profile_data() -> dict:
    """Sample profile data for testing."""
    return {
        "dataset_info": {
            "num_rows": 4,
            "num_columns": 4,
            "columns": ["name", "age", "city", "salary"]
        },
        "column_profiles": {
            "name": {"type": "string", "unique_count": 4},
            "age": {"type": "integer", "min": 25, "max": 35},
            "city": {"type": "string", "unique_count": 4},
            "salary": {"type": "integer", "min": 50000, "max": 60000}
        }
    }


@pytest.fixture
def sample_dependency_data() -> dict:
    """Sample dependency data for testing."""
    return {
        "dependencies": [
            {"source": "age", "target": "salary", "strength": 0.8},
            {"source": "city", "target": "salary", "strength": 0.6}
        ],
        "constraints": [
            {"column": "age", "constraint": "between", "min": 18, "max": 65},
            {"column": "salary", "constraint": "positive"}
        ]
    }


@pytest.fixture
def app():
    """Create FastAPI app for testing."""
    return create_app()


@pytest.fixture
def client(app) -> TestClient:
    """Create test client."""
    return TestClient(app)


@pytest.fixture
async def async_client(app) -> AsyncGenerator[AsyncClient, None]:
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac