"""Integration tests for API endpoints."""

import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, AsyncMock


class TestAPIEndpoints:
    """Test cases for API endpoints."""

    def test_health_check(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"

    def test_detailed_health_check(self, client: TestClient):
        """Test detailed health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "DataEcho API"
        assert "components" in data

    @patch('backend.api_service.routers.sessions.session_manager')
    def test_create_session(self, mock_session_manager, client: TestClient):
        """Test session creation endpoint."""
        mock_session_manager.create_session = AsyncMock(return_value="test-session-id")
        
        response = client.post("/api/v1/sessions", json={
            "domain": "test-domain",
            "user_context": "test-context"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["session_id"] == "test-session-id"
        assert data["status"] == "created"

    @patch('backend.api_service.routers.sessions.session_manager')
    def test_get_session_status(self, mock_session_manager, client: TestClient):
        """Test get session status endpoint."""
        mock_session_manager.get_session = AsyncMock(return_value={
            "session_id": "test-session-id",
            "status": "running",
            "current_step": "profiling",
            "progress": 30,
            "message": "Processing data...",
            "data": {}
        })
        
        response = client.get("/api/v1/sessions/test-session-id/status")
        
        assert response.status_code == 200
        data = response.json()
        assert data["session_id"] == "test-session-id"
        assert data["status"] == "running"
        assert data["progress"] == 30

    @patch('backend.api_service.routers.sessions.session_manager')
    def test_get_session_status_not_found(self, mock_session_manager, client: TestClient):
        """Test get session status for non-existent session."""
        mock_session_manager.get_session = AsyncMock(return_value=None)
        
        response = client.get("/api/v1/sessions/non-existent/status")
        
        assert response.status_code == 404
        assert "Session not found" in response.json()["detail"]

    @patch('backend.api_service.routers.sessions.storage_manager')
    @patch('backend.api_service.routers.sessions.session_manager')
    def test_upload_csv(self, mock_session_manager, mock_storage_manager, client: TestClient):
        """Test CSV upload endpoint."""
        mock_storage_manager.save_uploaded_file = AsyncMock(return_value="/test/path/file.csv")
        mock_session_manager.update_session = AsyncMock()
        
        # Create a test CSV file
        csv_content = "name,age\nJohn,30\nJane,25"
        
        response = client.post(
            "/api/v1/sessions/test-session-id/upload-csv",
            files={"file": ("test.csv", csv_content, "text/csv")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "CSV file uploaded successfully" in data["message"]

    def test_upload_invalid_file(self, client: TestClient):
        """Test uploading non-CSV file."""
        response = client.post(
            "/api/v1/sessions/test-session-id/upload-csv",
            files={"file": ("test.txt", "not csv", "text/plain")}
        )
        
        assert response.status_code == 400
        assert "Only CSV files are allowed" in response.json()["detail"]