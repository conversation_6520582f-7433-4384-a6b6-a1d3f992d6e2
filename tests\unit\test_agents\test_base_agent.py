"""Tests for BaseAgent class."""

import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime

from dataecho.agents.base_agent import BaseAgent
from dataecho.models.agents import AgentType, AgentStatus


class TestBaseAgent:
    """Test cases for BaseAgent."""

    class ConcreteAgent(BaseAgent):
        """Concrete implementation of BaseAgent for testing."""
        
        def _get_default_config(self):
            return {"test_config": True}
        
        async def process(self, input_data):
            return {"result": "processed", "input": input_data}

    @pytest.fixture
    def agent(self):
        """Create a test agent instance."""
        return self.ConcreteAgent(AgentType.PROFILER, "test-session-id")

    def test_agent_initialization(self, agent):
        """Test agent is properly initialized."""
        assert agent.agent_type == AgentType.PROFILER
        assert agent.session_id == "test-session-id"
        assert agent.state.status == AgentStatus.PENDING
        assert agent.state.config == {"test_config": True}
        assert agent.agent_id is not None

    @pytest.mark.asyncio
    async def test_successful_run(self, agent):
        """Test successful agent run."""
        input_data = {"test": "data"}
        
        result = await agent.run(input_data)
        
        assert result == {"result": "processed", "input": input_data}
        assert agent.state.status == AgentStatus.COMPLETED
        assert agent.state.input_data == input_data
        assert agent.state.output_data == result

    @pytest.mark.asyncio
    async def test_failed_run(self, agent):
        """Test agent run with exception."""
        input_data = {"test": "data"}
        
        # Mock the process method to raise an exception
        with patch.object(agent, 'process', side_effect=Exception("Test error")):
            with pytest.raises(Exception, match="Test error"):
                await agent.run(input_data)
        
        assert agent.state.status == AgentStatus.FAILED
        assert agent.state.error_message == "Test error"

    def test_get_state(self, agent):
        """Test get_state returns current state."""
        state = agent.get_state()
        assert state.agent_type == AgentType.PROFILER
        assert state.session_id == "test-session-id"
        assert state.status == AgentStatus.PENDING