"""Tests for SessionManager class."""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, AsyncMock

from dataecho.services.session_manager import SessionManager


class TestSessionManager:
    """Test cases for SessionManager."""

    @pytest.fixture
    def temp_sessions_dir(self):
        """Create temporary sessions directory."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def session_manager(self, temp_sessions_dir):
        """Create SessionManager with temporary directory."""
        with patch('app.config.settings.session_storage_dir', str(temp_sessions_dir)):
            return SessionManager()

    @pytest.mark.asyncio
    async def test_create_session(self, session_manager, temp_sessions_dir):
        """Test session creation."""
        domain = "test-domain"
        user_context = "test-context"
        
        session_id = await session_manager.create_session(domain, user_context)
        
        assert session_id is not None
        assert len(session_id) == 36  # UUID length
        
        # Check session file was created
        session_file = temp_sessions_dir / f"{session_id}.json"
        assert session_file.exists()
        
        # Check session content
        with open(session_file) as f:
            session_data = json.load(f)
        
        assert session_data["session_id"] == session_id
        assert session_data["domain"] == domain
        assert session_data["user_context"] == user_context
        assert session_data["status"] == "created"

    @pytest.mark.asyncio
    async def test_get_session_existing(self, session_manager, temp_sessions_dir):
        """Test getting an existing session."""
        # Create a session first
        session_id = await session_manager.create_session("test-domain", "test-context")
        
        # Get the session
        session = await session_manager.get_session(session_id)
        
        assert session is not None
        assert session["session_id"] == session_id
        assert session["domain"] == "test-domain"

    @pytest.mark.asyncio
    async def test_get_session_nonexistent(self, session_manager):
        """Test getting a non-existent session."""
        session = await session_manager.get_session("non-existent-id")
        assert session is None

    @pytest.mark.asyncio
    async def test_update_session(self, session_manager, temp_sessions_dir):
        """Test updating session data."""
        # Create a session first
        session_id = await session_manager.create_session("test-domain", "test-context")
        
        # Update the session
        update_data = {"status": "running", "progress": 50}
        await session_manager.update_session(session_id, update_data)
        
        # Verify the update
        session = await session_manager.get_session(session_id)
        assert session["status"] == "running"
        assert session["progress"] == 50

    @pytest.mark.asyncio
    async def test_delete_session(self, session_manager, temp_sessions_dir):
        """Test deleting a session."""
        # Create a session first
        session_id = await session_manager.create_session("test-domain", "test-context")
        
        # Verify session exists
        assert await session_manager.get_session(session_id) is not None
        
        # Delete the session
        await session_manager.delete_session(session_id)
        
        # Verify session is deleted
        assert await session_manager.get_session(session_id) is None
        
        # Verify file is deleted
        session_file = temp_sessions_dir / f"{session_id}.json"
        assert not session_file.exists()